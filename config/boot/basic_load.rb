# frozen_string_literal: true

## 1. Application configuration parsers and dependencies that we didn't touch yet;

require 'rubygems'
require 'bundler/setup'
require 'newrelic_rpm'

ENV['TZ'] = 'America/Sao_Paulo'

APP_ENV = ENV['RACK_ENV'] || 'development'

Bundler.require :default, APP_ENV

Time.zone = ENV.fetch('TZ')
Resque::Mailer.current_env = APP_ENV

require 'action_mailer'

require_relative '../../lib/auth_load'

AuthConfig = AuthLoad.load

unless Object.const_defined?(:AppConfig)
  config_file = File.join(__dir__, '../config.yml')

  AppConfig = YAML.safe_load(
    ERB.new(File.read(config_file)).result,
    aliases: true
  )[APP_ENV]
end

# Load all locale files used for all parts. Here is a big opportunity
# to split files by application or scope, like as 'models' and theirs 'locales'.
I18n.load_path += Dir[File.expand_path('../locales/**/*.yml', __dir__)]
I18n.default_locale = I18n.locale = :pt_BR
