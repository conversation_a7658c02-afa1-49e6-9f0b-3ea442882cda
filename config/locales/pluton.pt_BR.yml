---
pt_BR:
  unauthorized: ação não autorizada
  accepts_call: Receber notificações por ligações
  accepts_sms: Receber notificações por SMS
  admin_note:
    create_error: Erro ao cadstrar comentário.
    create_success: Comentário cadastrado com sucesso.
    title: Comentários
    removed: Comentário removido com sucesso.
    not_allowed: Você não possui permissão para executar essa ação.
    pinned: Comentário fixado com sucesso.
    unpinned: Comentário desfixado com sucesso.
  admin_user:
    create_success: Usuário cadastrado com sucesso.
    destroy_success: Usuário removido com sucesso.
    edit: Editar usuário
    edit_success: Usuário atualizado com sucesso.
    new: Novo usuário
    title: Usuários
  attributes:
    email: E-mail
    name: Nome
    phone: Telefone
  bank:
    checking: Corrente
    savings: Poupança
    pending: Em andamento
    success: Ok
    fail: Falhou
  blocked_address:
    cep: CEP
    create_success: Bloqueio cadastrado com sucesso.
    delete_success: Bloqueio removido com sucesso.
    edit_blocked_address: Editar bloqueio
    new_blocked_address: Novo bloqueio
    state: Estado
    title: Bloqueio de endereços
    update_success: Bloqueio atualizado com sucesso.
  blocked_taskers:
    created: Profissional "%{tasker_name}" bloqueado com sucesso.
    removed: Profissional "%{tasker_name}" removido com sucesso.
  blocks:
    background_text_action: Header CTA
    cleaning: Header de Limpeza
    cleaning_text_list: Header de Limpeza Secundário
    colors:
      green: Verde
      red: Vermelho
    custom: Personalizado
    icon_action: Ícones com botão
    image_text: Imagem e texto
    list_action: Lista de itens
    positions:
      left: Esquerda
      right: Direita
    quotes: Citações
    text: Apenas texto
    text_action: Texto com botão
    text_input: Texto e campo
  bonus:
    elegible: Elegível
    none: Nenhum
    one_time: Comissão
    recurrency: Valor Extra
  company:
    create_success: Empresa cadastrada com sucesso.
    destroy_success: Empresa removida com sucesso.
    edit: Editar empresa
    edit_success: Empresa atualizada com sucesso.
    new: Nova Empresa
    title: Empresas
  config:
    min_job:
      express_cleaning: Limpeza Express
      business_cleaning: Limpeza Comercial
      cleaning: Limpeza
      furniture_assembly: Montagem de Móveis
      handyman: Pequenos reparos
      heavy_cleaning: Limpeza Pesada
      ironing: Passadoria
      painting: Pintura
      pre_moving_cleaning: Limpeza pré-mudança
      remodeling_cleaning: Limpeza pós-obra
    sms:
      limits: Limites
      values:
        distance: Distância geográfica (km)
        else: NDA
        express_offers: Número de ofertas para job EXPRESS
        job_date_distance: Limite entre ofertas (horas)
        job_limit: Jobs para novo cliente
        less_3: Menos que 3 dias
        less_7: Menos de uma semana
        list_limit: Quantidade de prestadores
        min_score: Nota mínima de feedback
        offers: Número de ofertas para gerar
        score_4: Nota 4
        score_5: Nota 5
    titles:
      min_job: Limites por região
      sms: Configurações de SMS
    weight: Peso
  coupon:
    actions:
      expire: Expirar
    create_success: Cupom cadastrado com sucesso.
    edit_success: Cupom atualizado com sucesso.
    expire: Você tem certeza que deseja expirar este Cupom?
    expire_success: Cupom expirado com sucesso.
    frequencies:
      single: Diária única
      once_week: Toda semana
      once_2_weeks: Toda quinzena
      once_month: Todo mês
    new_coupon: Cadastrar Cupom
    one_per_customer: Único por cliente
    one_use: Único
    percentage: Porcentagem
    single: Único
    subscription: Assinatura
    title: Cupom
    unlimited: Ilimitado
    value: Valor
  credit_card:
    title: Cartões
    link_error: Não conseguimos vincular o cartão.
    link_success: Cartão vinculado ao cliente e marcado como principal com sucesso.
    update_error: Não conseguimos atualizar o Cartão.
    update_success: Cartão atualizado como principal com sucesso.
    verify: Verificar Cartão
    verify_confirm: Deseja realmente verificar o cartão desse cliente? Ele será notificado em caso de falha.
    verify_success: Cartão verificado. Verifique seu status.
  device:
    title: Dispositivos
    update_error: Não conseguimos atualizar este dispositivo.
    update_success: Dispositivo atualizado com sucesso.
  criticable:
    types:
      conciliate: Conciliar agendas
      courtesy: Diária cortesia
      low_feedback_score: Feedback baixo
      no_show: Falta
  deal:
    actions:
      cancel: Cancelar
    cancel: Você tem certeza que deseja cancelar este acordo?
    cancel_success: Acordo cancelado com sucesso.
    create_success: Acordo cadastrado com sucesso.
    new_deal: Novo acordo
    title: Acordos
  devise:
    confirmations:
      confirmed: Sua conta foi confirmada com sucesso. Você está logado.
      send_instructions: Dentro de minutos, você receberá um e-mail com instruções para a confirmação da sua conta.
      send_paranoid_instructions: Se o seu endereço de e-mail estiver cadastrado, você receberá uma mensagem com instruções para confirmação da sua conta.
    failure:
      admin_user:
        already_authenticated: Você já está logado.
        inactive: Sua conta ainda não foi ativada.
        invalid: E-mail ou senha inválidos.
        invalid_token: O token de autenticação não é válido.
        locked: Sua conta está bloqueada.
        not_found_in_database: E-mail ou senha inválidos.
        timeout: Sua sessão expirou, por favor, efetue login novamente para continuar.
        unauthenticated: Para continuar, efetue login ou registre-se.
        unconfirmed: Antes de continuar, confirme a sua conta.
    mailer:
      confirmation_instructions:
        subject: Instruções de confirmação
      reset_password_instructions:
        subject: Instruções de troca de senha
      unlock_instructions:
        subject: Instruções de desbloqueio
    omniauth_callbacks:
      failure: Não foi possível autenticá-lo como %{kind} porque "%{reason}".
      success: Autenticado com sucesso com uma conta de %{kind}.
    password:
      complexity_unmet: A senha deve conter ao menos 8 caracteres, incluindo letra maiúscula, minúscula, número e caracter especial.
    passwords:
      no_token: Você só pode acessar essa página através de um e-mail de troca de senha. Se já estiver acessando por um e-mail, verifique se a URL fornecida está completa.
      send_instructions: Dentro de minutos, você receberá um e-mail com instruções para a troca da sua senha.
      send_paranoid_instructions: Se o seu endereço de e-mail estiver cadastrado, você receberá um link de recuperação da senha via e-mail.
      updated: Sua senha foi alterada com sucesso. Você está logado.
      updated_not_active: Sua senha foi alterada com sucesso.
    registrations:
      destroyed: Tchau! Sua conta foi cancelada com sucesso. Esperamos vê-lo novamente em breve.
      signed_up: Login efetuado com sucesso. Se não foi autorizado, a confirmação será enviada por e-mail.
      signed_up_but_inactive: Você foi cadastrado com sucesso. No entanto, não foi possível efetuar login, pois sua conta não foi ativada.
      signed_up_but_locked: Você foi cadastrado com sucesso. No entanto, não foi possível efetuar login, pois sua conta está bloqueada.
      signed_up_but_unconfirmed: Uma mensagem com um link de confirmação foi enviada para o seu endereço de e-mail. Por favor, abra o link para confirmar a sua conta.
      update_needs_confirmation: Você atualizou a sua conta com sucesso, mas o seu novo endereço de e-mail precisa ser confirmado. Por favor, acesse-o e clique no link de confirmação que enviamos.
      updated: Sua conta foi atualizada com sucesso.
    sessions:
      signed_in: Login efetuado com sucesso!
      signed_out: Saiu com sucesso.
    unlocks:
      send_instructions: Dentro de minutos, você receberá um email com instruções para o desbloqueio da sua conta.
      send_paranoid_instructions: Se sua conta existir, você receberá um e-mail com instruções para desbloqueá-la em alguns minutos.
      unlocked: Sua conta foi desbloqueada com sucesso. Efetue login para continuar.
  entry:
    new_entry: Novo Lançamento
    pay_tasker: 'Pagamento semanal %{amount}'
    pay_on_demand: 'Pagar sob-demanda %{amount}'
    payment_failed:
      payout_unaccepted: O Pagamento não pôde ser processado. Este profissional está com a opção de receber pagamentos (aceitar payout) desabilitada no perfil.
      unpermitted_withdraw: O Pagamento não pôde ser processado. Profissional sem categoria não pode sacar sob demanda.
      insufficient_balance: O Pagamento não pôde ser processado. Saldo insuficiente para saque.
      irregular_statement: O Pagamento não pôde ser processado. Extrato irregular, é necessário ajuste manual.
      unchecked_bank_account: O Pagamento não pôde ser processado. A conta bancária do profissional não foi validada.
    payment_requested: Pagamento Solicitado Com Sucesso
  errors:
    messages:
      already_confirmed: já foi confirmado
      confirmation_period_expired: precisa ser confirmada em até %{period}, por favor, solicite uma nova
      expired: expirou, por favor, solicite uma nova
      not_found: não encontrado
      not_locked: não foi bloqueado
      not_saved:
        one: 'Não foi possível salvar %{resource}: 1 erro'
        other: 'Não foi possível salvar %{resource}: %{count} erros.'
  event:
    title: Eventos
  external_account:
    labels:
      errored: error
      new: info
      validated: success
      verification_failed: error
      verified: success
    states:
      errored: Com erro (favor atualizar)
      new: Nova
      validated: Validada
      verification_failed: Verificação Falhou
      verified: Verificada
  'false': Não
  feedback:
    title: Feedbacks
    removed: Feedback removido com sucesso.
    not_allowed: Você não possui permissão para executar essa ação.
  processed_feedback:
    enabled:
      true: Habilitado
      false: Desabilitado
    disabled_success: Feedback processado desabilitado com sucesso.
    enabled_success: Feedback processado habilitado com sucesso.
    not_allowed: Você não possui permissão para executar essa ação.
  tasker_feedback:
    title: Feedbacks de profissional
  furniture_assembly: Montagem de Móveis
  notification:
    failed: Falhou
    received: Recebido
    success: Enviado
  handyman: Pequenos reparos
  hotsite:
    actions:
      duplicate: Duplicar
      publish: Publicar
      unpublish: Despublicar
      view: Visualizar
      remove: Deletar
    create_success: Hotsite cadastrado com sucesso.
    remove: Você tem certeza que deseja deletar este Hotsite?
    remove_success: Hotsite deletado com sucesso.
    duplicate: Você tem certeza que deseja duplicar este Hotsite?
    duplicate_success: Hotsite duplicado com sucesso.
    edit_success: Hotsite atualizado com sucesso.
    new_hotsite: Cadastrar Hotsite
    publish: Você tem certeza que deseja publicar este Hotsite?
    publish_success: Hotsite publicado com sucesso.
    published:
      'false': Rascunho
      'true': Publicado
    title: Hotsite
    unpublish: Você tem certeza que deseja despublicar este Hotsite?
    unpublish_success: Hotsite despublicado com sucesso.
  incidents:
    admin: Admin
    cancel: Cancelamento
    cancel_success: Profissional removido do job com sucesso.
    contacted:
    create_success: Incidente criado com sucesso.
    delayed: Atraso
    no_show: Falta
    no_show_success: Falta registrada com sucesso.
    normal: Normal
    high: Alta
    operations: Via Operacional
    sms: Via SMS
    tasker: Profissional
    title: Incidentes
    user: Cliente
    feedback_exists: Incidente não pode ser removido pois existe feedback(%{feedback_id}) associado.
    incident_removed: Incidente removido.
  invoice:
    full: Cobrança Total
    job: Job
    tip: Gorjeta
    monthly_billing_alert: Esta fatura será cobrada numa fatura mensal
    pack: Pacote
    partial: Cobrança Parcial (Multa)
    reschedule: Reagendamento
    reschedule_full: Cobrança total de reagendamento
    reschedule_partial: Cobrança Parcial de reagendamento
    skip: Pular Diária
    skip_full: Cobrança total de reagendamento
    skip_partial: Cobrança Parcial de reagendamento
    state:
      cancelled: Cancelado
      credited: Creditado à Diarista
      packed: Acumulado
      paid: Pago
      refunded: Reembolsado
      unpaid: Não Pago
  iugu:
    accepted: Disponível na conta bancária
    account: Prestador
    bank: Banco
    cancelled: Cancelado
    cpf: CPF
    pending: Aguardando envio ao Banco
    processing: Em pagamento
    rejected: Rejeitado
    short:
      accepted: Confirmado
      cancelled: Cancelado
      pending: Pendente
      processing: Pagando
      rejected: Rejeitado
      unknown: Desconhecido
    unknown: Não temos informações sobre confirmação de pagamento.
  iugu_invoices:
    refund: Você tem certeza que deseja estornar esta fatura iugu?
    title: Faturas Iugu Job %{number}
  job:
    actions:
      cancel: Cancelar
      change: Alterar opcionais
      complete: Concluir
      edit: Editar
      force_charge: Forçar pré-autorização
      invoices: Ver Faturas Iugu
      pay: Marcar Como Pago
      payment_confirm: Enviar recibo
      reschedule: Reagendar
      skip: Pular
      uncomplete: Desconcluir
    cancel: 'Você tem certeza que deseja cancelar este Job? Todos os prestadores serão avisados via SMS do cancelamento. O Cliente será avisado por email. ATENÇÃO: O cancelamento é permanente, considere utilizar a opção DESMARCAR.'
    cancel_success: Job cancelado com sucesso.
    change_success: Job atualizado com sucesso.
    freeze_chat: Você tem certeza que deseja encerrar esse chat?
    freeze_chat_success: Chat enviado para encerramento. O processo pode levar alguns segundos. Por favor, espere alguns instantes e atualize a página.
    complete: Ao completar o job os profissionais receberão o valor do job e será solicitado o feedback ao cliente.
    complete_success: Job concluído com sucesso.
    edit_success: Job atualizado com sucesso.
    force_charge: Você tem certeza que desejar realizar uma nova tentativa de cobrança no cartão do cliente?
    force_charge_error: Não foi possível forçar uma nova cobrança deste job.
    force_charge_success: Nova tentativa de cobrança realizada com sucesso.
    new_order: Novo Job
    pay: Atenção, o cliente não será cobrado automaticamente no cartão de credito ao marcar este pedido como pago. Garanta que o valor já foi recebido antes de continuar.
    pay_success: Marcação de Job como pago bem-sucedida.
    payment_confirm_success: Confirmação de pagamento enviada.
    reschedule_success: Job reagendado com sucesso.
    reschedule_unavailable:
      incompatible_service: Este tipo de serviço não pode ser reagendado.
      invalid_job: 'Este job não é válido (ex: sem usuário).'
    skip: Você tem certeza que deseja pular este Job? Todos os prestadores serão avisados via SMS desta ação, o cliente será avisado por email.
    skip_success: Job pulado com sucesso.
    tasker_removed: Profissional removido do serviço sem criação de incidente.
    title: Job
    uncomplete: Ao desconcluir será realizado o estorno nas contas dos prestadores que já receberam por este job e o job ficará pendente.
    uncomplete_motive: Estorno de diária não realizada
    uncomplete_success: Job desconcluído com sucesso.
  job_tasker:
    confirm_success: Prestador confirmado com sucesso.
    reject_success: Prestador rejeitado com sucesso.
    check_in_holdback:
      true: Sim.
      false: Não.
      wrong_address: Endereço incorreto.
      absent_customer: Cliente ausente.
      custom: Outro motivo.
  check_in_holdback_solved: Dificuldade para iniciar resolvida
  label:
    accept: label-success
    reject: label-danger
    unknown: label-warning
  list: Lista
  mark_as_critical: Marcar como crítico.
  meta_tags:
  - key_name: name
    key_value: description
    content: "✓ Descrição Aqui"
  - key_name: name
    key_value: keywords
  - key_name: name
    key_value: robots
    content: index, follow
  - key_name: property
    key_value: og:type
    content: website
  - key_name: property
    key_value: og:url
  - key_name: property
    key_value: og:title
  - key_name: property
    key_value: og:description
    content: "✓ Descrição Aqui"
  - key_name: property
    key_value: og:image
  - key_name: property
    key_value: og:locale
    content: pt_BR
  - key_name: property
    key_value: og:site_name
    content: Parafuzo.com
  - key_name: property
    key_value: og:image:type
    content: image/jpg
  - key_name: property
    key_value: og:image:width
  - key_name: property
    key_value: og:image:height
  - key_name: name
    key_value: twitter:card
    content: summary_large_image
  - key_name: name
    key_value: twitter:site
  - key_name: name
    key_value: twitter:creator
  - key_name: name
    content: "@parafuzocasa"
    key_value: twitter:title
  - key_name: name
    key_value: twitter:description
    content: "✓ Descrição Aqui"
  - key_name: name
    key_value: twitter:img:src
  mongoid:
    attributes:
      blocked_address:
        motive: Motivo
        type: Tipo
        value: Valor
      coupon:
        code: Código
        expiration: Expiração
        frequencies: Assinaturas
        frequency_type: Frequência
        hours: Horários de Início
        job_quantity: Número de jobs
        max_job_duration: Duração máxima do Job
        min_job_duration: Duração mínima do Job
        min_price: Valor mínimo do pedido
        regions: Regiões
        service: Serviço
        type: Tipo
        usage_type: Tipo de uso
        value: Valor
        wdays: Dias da Semana
      deal:
        amount: Total
        category: Categoria
        description: Descrição
        installments: Parcelas
      service:
        enabled: Habilitado
  order:
    cancel: Você tem certeza que deseja cancelar este pedido? Todos os jobs serão cancelados, e os prestadores serão alertados do cancelamento via SMS. O Cliente será avisado via email.
    cancel_success: Pedido cancelado com sucesso.
    checkout_success: Pedido solicitado com sucesso.
    create_success: Pedido criado com sucesso.
    finish: Você tem certeza que deseja encerrar este pedido? Os jobs em aberto serão cancelados, e os prestadores serão alertados do cancelamento via SMS. O Cliente será avisado via email.
    finish_success: Pedido encerrado com sucesso.
    invoice: Você tem certeza que deseja faturar este pedido? Será gerada uma nova fatura e o Cliente será avisado via email.
    invoice_invalid_service: Este pedido não pode ser faturado.
    invoice_success: Pedido faturado com sucesso.
    invoice_tooltip: Todo pedido comercial já é faturado no dia 20 de cada mês, utilize essa ação somente em casos especiais.
    new_bill: Você tem certeza que deseja gerar um novo boleto para este pedido?
    new_order: Novo Pedido
    pause: Você tem certeza que deseja pausar este pedido? Os jobs em aberto serão pausados também. O Cliente será avisado via email.
    pause_success: Pedido pausado com sucesso.
    pause_tooltip: 'Utilize este botão somente para problemas de pagamento. ATENÇÃO: Se um cliente solicitar pausar uma assinatura, pergunte a data que ele volta de viagem e reagende os serviços dele para a partir desta data.  Caso o cliente rejeite ou não tenha data de volta, finalize a assinatura dele e oriente-o a contratar novamente via site quando retornar.'
    pay: Marcar este pedido como pago?
    property_type:
      apartment: Apartamento
      house: Casa
      studio: Studio
      not_found: "Tipo de imóvel não informado"
    resume: Você tem certeza que deseja reativar este pedido? Os jobs pausados serão reativados. O Cliente será avisado via email.
    resume_success: Pedido reativado com sucesso.
    subscription_type:
      single: Diária única
      once_week: Toda semana
      once_2_weeks: Toda quinzena
      once_month: Todo mês
    title: Pedidos
    update_success: Pedido atualizado com sucesso.
  painting: Pintura
  checkout_error:
    blocked_address: O endereço do pedido está bloqueado.
    user_indebted: O usuário está negativado.
    invalid_order: O pedido está com dados inválidos.
    credit_card_payment_failed: Houve um problema com o cartão de crédito do usuário.
    stripe_payment_failed: Houve um problema durante o processamento do pedido pela Stripe.
    iugu_payment_failed: Houve um problema durante o processamento do pedido pela Iugu.
  parameters:
    bathroom_quantity:
      one: 1 banheiro
      other: "%{count} banheiros"
    bedroom_quantity:
      one: 1 quarto
      other: "%{count} quartos"
    big_apartment: Apartamento grande - mais de dois quartos
    big_house: Casa grande - mais de dois quartos
    busy_apartment: Apartamento ocupado.
    busy_house: Casa ocupada.
    cleaning:
      apartment: Apartamento
      big: Grande
      cleanup_cabinets: Limpar interior de armários
      cleanup_external: Limpar área externa
      cleanup_heavy: Faxina pesada
      house: Casa
      ironing: Passar roupas
      laundry: Lavar e estender roupas
      refrigerator: Limpar geladeira
      small: Pequeno(a)
      tasker: Diarista
      vaccum_carpet: Aspirar carpetes e estofados
      wash_windows: Limpar janelas
      bring_products: Levar produtos
    business_cleaning:
      unknown_area: Não sei a metragem
    door_quantity:
      one: 1 porta
      other: "%{count} portas"
    empty_apartment: Apartamento vazio.
    empty_house: Casa vazia.
    furniture_assembly:
      armchair: Poltrona
      attributes:
        armchair_type: Tipo
        armoire_type: Tipo
        bed_type: Tamanho
        chair_type: Tipo
        chamber_type: Tipo
        sofa_type: Tipo
        doors_quantity: Qtd. Portas
        drawers_quantity: Qtd. Gavetas
        lockers_quantity: Qtd. Gavetas
        dresser_type: Tamanho
        fix_on_wall: Fixo na parede
        furniture_type: Tipo
        has_drawers: Gaveta
        has_mirror: Espelho
        has_panel: Painel
        has_shelf: Prateleira
        is_bed: Sofá-cama
        is_retractable: Retrátil
        mirrors_quantity: Qtd. Espelhos
        nightstand_type: Tamanho
        optionals:
          adult: Adulto
          breastfeeding: Amamentação
          common: Comum
          child: Infantil
          couple: Casal
          decorative: Decorativa
          'false': Não
          full: Casal
          garden: Jardim
          kid: Infantil
          kitchen: Cozinha
          king_size: King Size
          living_room: Sala de Jantar/Estar
          multifunctional: Multifuncional
          office: Escritório
          recliner: Reclinável
          single: Solteiro
          'true': Sim
          gamer: Gamer
          table_side: Mesa lateral
          double_bed: Bicama
          bunkbed: Beliche
          treliche: Treliche
          kitchen_chamber: De Cozinha
          bathroom_chamber: De Banheiro
          kitchen_cabinet: De Cozinha
          laundry_cabinet: De Lavanderia
          office_cabinet: De Escritório
          gym_locker: De Academia
          montessori_single: Montessoriana de Solteiro
          montessori_couple: Montessoriana de Casal
          sofa_bed: Sofá Cama
        shelfs_quantity: Qtd. Prateleiras
        stool_type: Tipo
        table_type: Tipo
        unities_quantity: Qtd.
        armoire_installation: Instalação de armário
        panel_installation: Instalação de painel
        cabinet_installation: Instalação de cabine
        stand_installation: Instalação de prateleira
        suspended_module_installation: Instalação de módulo aéreo
        headboard_installation: Instalação de cabeceira de cama
        tv_stand_installation: Instalação de rack
        decorative_niche_installation: Instalação de nicho decorativo
        chamber_installation: Instalação de gabinete
        wardrobe_type: Tipo
      armoire: Armário
      bed: Cama
      buffet: Buffet
      cabinet: Armário
      kitchen_cabinet: Armário de cozinha
      chair: Cadeira
      china_cabinet: Cristaleira
      closet: Armário
      cupboard: Cristaleira
      display_cabinet: Cristaleira
      drawer: Gaveteiro
      filing_cabinet: Gaveteiro
      dresser: Cômoda
      furniture: Guarda-Roupa
      nightstand: Mesa de cabeceira
      rack: Rack
      tv_stand: Rack
      shelf: Estante
      bookcase: Estante
      shoemaker: Sapateira
      shoe_rack: Sapateira
      sideboard: Aparador
      sofa: Sofá
      stool: Bancos & Banquetas
      seat: Bancos & Banquetas
      table: Mesa
      tv_panel: Painel de TV
      wardrobe: Guarda-roupa
      dressing_table: Penteadeira
      cradle: Berço
      chamber: Gabinete
      decorative_niche: Nicho Decorativo
      headboard: Cabeceira de Cama
      chest: Baú/Caixa
      stand: Prateleira
      suspended_module: Módulo Aéreo
      writing_table: Escrivaninha
      coat_rack: Arara
      recamier: Recamier
      counter: Balcão
      pan_cabinet: Paneleiro
    handyman:
      fix_cloggings: Privada ou pia entupida
      fix_courtains: Instalação ou manutenção de cortinas
      fix_doors: Manutenção de Portas
      fix_dripping_faucet: Torneira pingando
      fix_leakings: Vazamentos
      fix_locks: Fechaduras que não estão funcionando direito
      fix_shower: Manutenções gerais de chuveiros
      fix_sink: Manutenções Gerais de Pias
      fix_windows: Janelas com aberturas
      install_frame: Pendurar quadros
      install_lamp: Instalação de Luminárias
      install_shelf: Instalação de Prateleiras
      replace_faucet_rubber: Troca de borracha de torneira
    heavy_cleaning:
      tasker: Diarista
    info_not_available: Informação não disponível.
    ironing_cleaning:
      tasker: Diarista
    none_chosen: Nenhum escolhido.
    painting:
      apartment: Apartamento
      bathroom: Banheiros
      busy: Imóvel ocupado
      corridor: Corredores
      dining_room: Sala de jantar
      empty: Imóvel vazio
      entire_property: Pintura de imóvel inteiro.
      extra_cleaning: Serviço de limpeza
      extra_electric: Revisão elétrica
      extra_plumbing: Revisão hidráulica
      extra_warranty: Garantia extendida
      footer_painting: Rodapé/Rodateto
      house: Casa
      kitchen: Cozinha
      laundry_room: Área de serviço
      living_room: Sala de estar
      paint_purchase:
        customer: Cliente fornece
        parafuzo: Parafuzo fornece
        unknow: Não informado
      paint_type:
        hypo: Tinta Antialérgica
        premium: Tinta Premium
        regular: Tinta Comum
      partial_property: Pintura de alguns cômodos.
      tasker: Prestador
      walls:
        bad: Paredes precisam de muitos reparos
        good: Paredes em bom estado
        regular: Paredes precisam de reparos leves
    pre_moving_cleaning:
      tasker: Diarista
    property_area: Área de %{number}m2
    remodeling_cleaning:
      tasker: Diarista
    small_apartment: Apartamento pequeno - até dois quartos
    small_house: Casa pequena - até dois quartos
  payment:
    accepted: Confirmado
    cancelled: Cancelado
    captured: Capturado
    created: Criado
    denied: Negado
    failed: Falhou
    origin:
      capture: Cobrança
      charge: Autorização
    pending: Pendente
    refunded: Estornado
  payments:
    refund: Você tem certeza que deseja estornar este pagamento? O cliente será avisado por email.
    refund_fail: Não foi possível realizar o estorno. Mensagem - %{message}
    refund_success: Pagamento estornado com sucesso.
    title: Histórico de pagamentos
  payout_name: Pagamento
  confirmations: Confirmações
  payout_scope:
    accepted: Confirmado
    cash: Dinheiro
    check: Cheque
    credit: Crédito
    debit: Débito
    deposit: Depósito
    entries_quantity:
      one: 1 entrada
      other: "%{count} entradas"
    failed: Falhou
    not_ok_to_work: 'Profissional inválido para iniciar. Corrigir: %{errors}'
    ok_to_work: Profissional válido para iniciar
    payment: Pagamento
    pending: Pendente
    processing: Pagando
    rejected: Rejeitado
    transfer: Transferência
    unknown: Desconhecido
    bank_receipt: Comprovante de pagamento
  photo:
    crop_success: Imagem recortada com sucesso.
  preferential:
    confirm_success: Convite preferencial confirmado com sucesso.
    confirm_error: Houve um erro durante o aceite do convite preferencial.
    pick_other: 'Utilize este botão para remover a profissional caso você tenha selecionado uma profissional errada ao adicionar. ATENÇÃO: Se você já tiver salvado a profissional, utilize o botão vermelho.'
    remove: Utilize este botão para remover profissional preferencial. Ela não irá mais atender este cliente recorrentemente. Para aplicar a alteração, você precisará salvar o pedido.
  price: Preço
  regions:
    balneario-camboriu: Balneário Camboriú
    barra-velha: Barra Velha
    belem: Belém
    belo-horizonte: Belo Horizonte
    bertioga: Bertioga
    blumenau: Blumenau
    brasilia: Brasília
    campinas: Campinas
    campo-grande: Campo Grande
    campo-mourao: Campo Mourão
    cuiaba: Cuiabá
    curitiba: Curitiba
    florianopolis: Florianópolis
    fortaleza: Fortaleza
    goiania: Goiânia
    guarapuava: Guarapuava
    itaquaquecetuba: Itaquaquecetuba
    joao-pessoa: João Pessoa
    joinville: Joinville
    juiz-de-fora: Juíz de Fora
    jundiai: Jundiaí
    londrina: Londrina
    maceio: Maceió
    maringa: Maringá
    mogi-das-cruzes: Mogi das Cruzes
    natal: Natal
    paranavai: Paranavaí
    poa: Poá
    ponta-grossa: Ponta Grossa
    porto-alegre: Porto Alegre
    recife: Recife
    ribeirao-preto: Ribeirão Preto
    rio-de-janeiro: Rio de Janeiro
    salvador: Salvador
    sao-jose-dos-campos: São José dos Campos
    sao-paulo: São Paulo
    suzano: Suzano
    telemaco-borba: Telêmaco Borba
    uberlandia: Uberlândia
    umuarama: Umuarama
    vargem-grande-paulista: Vargem Grande Paulista
    vinhedo: Vinhedo
    vitoria: Vitória
  registry: Cadastro
  roles:
    owner: Ultra-power-admin
    admin: Administrador
    financial: Financeiro
    sales: Vendas
    sales_admin: Admin de Vendas
    user: Usuário
  run_order_service: Atualizar preços
  send_email: Notificar cliente
  send_sms: Ativar ofertas por SMS
  service:
    title: Serviços
    update_success: Serviço atualizado com sucesso.
  simple_form:
    labels:
      coupon:
        after_n_jobs: Após N diárias
      job:
        job_taskers:
          discount: Desconto
          payout: Payout base
          final_payout: Payout final
          user_payout: Total Cliente
          work_time: Tempo de Serviço
          confirmed_at_date: Eu vou (data)
          confirmed_at_time: Eu vou (hora)
          pre_check_in_at_time: Estou indo
          eta_time: Chegada estimada
          check_in_at_time: Cheguei
          check_out_at_time: Finalizei
      stored_config:
        stored_options:
          active: Ativo
      tasker:
        notify_type: Tipo de notificação
  sms:
    accept: Aceitou
    reject: Rejeitou
    unknown: Não entendido
  states:
    accepted: Aceita
    approved: Aprovado
    assigned: Atribuído
    cancelled: Cancelado
    completed: Concluído
    finished: Encerrado
    invoiced: Faturado
    ordered: Solicitado
    paid: Pago
    paused: Pausado
    pending: Pendente
    quoting: Orçamento
    rejected: Rejeitada
    skipped: Pulado
  stored_config:
    edit_success: Configuração atualizada com sucesso.
  table:
    author: Autor
    action: Ação
    active_name: Situação
    actor: Atuante
    admin_user: Usuário
    amount: Total
    antecedence: Aviso Recebido
    assigned: Atribuído à
    bill_date: Vencimento Boleto
    category: Categoria
    comment: Comentário
    city: Cidade
    code: Código
    context: Contexto
    created_at: Criado em
    credit_card:
      brand: Bandeira
    date: Data
    deadline: Prazo
    debtor_at: Início Inadimplência
    cpf_cnpj: CPF/CNPJ
    email: Email
    enabled: Habilitado
    expiration: Expiração
    frequency: Frequência
    hours: Horas
    installments: Parcelas
    issued_at: Emitido Em
    job_date: Data de Atendimento
    last_order_date: Data último pedido
    last_order_number: Último pedido
    last_sign_in_at: Último login
    main: Principal
    mode: Modo
    motive: Motivo
    name: Nome
    neighborhood: Bairro
    note: Nota
    number: Número
    order_number: Pedido
    phone: Telefone
    price: Preço
    refunded_amount: Estornado
    work_time: Duração
    published: Publicado
    questions: Respostas
    regions: Regiões
    review: Comentário
    role: Perfil
    score: Nota
    scored_at: Data da avaliação
    service: Serviço
    service_date: Data do serviço
    services: Serviços
    severity: Criticidade
    sign_in_count: Número de logins
    source: Fonte
    state: Estado
    status: Status
    tasker: Prestador
    tasker_name: Prestador
    taskers_names: Profissionais
    text: Texto
    tip: Gorjeta
    title: Título
    type: Tipo
    usage_type: Tipo de uso
    user: Cliente
    value: Valor
    zone_names: Zona
    uuid: Identificador
    app_version: Versão do app
    last_login_at: Último login
    last_logout_at: Último logout
    pre_check_in_at: Estou a caminho
    check_in_at: Cheguei
    check_out_at: Finalizei
  tasker:
    activate_success: Prestador ativado com sucesso.
    create_success: Prestador cadastrado com sucesso.
    deactivate_success: Prestador desativado com sucesso.
    edit_success: Prestador atualizado com sucesso.
    reset_success: Um email com a nova senha foi enviado para o profissional.
    without_email: Reset da senha falhou. O profissional precisa ter um email cadastrado.
    reset_failure:  Reset da senha falhou.
    reset_password_tasker:  Resetar senha do aplicativo
    confirm_reset: Você tem certeza que deseja resetar a senha deste profissional? Esta ação é irreversível!
    confirm_deactivate: Deseja desativar este prestador permanentemente? Irá remover a profissional dos jobs futuros e irá retirar também a profissional dos pedidos em que está marcada como preferencial.
    background_check:
      success: Prestador enviado para verificação dos antecedentes criminais.
    mei_check:
      success: Prestador enviado para verificação do MEI.
      failure: Verificar dados do MEI.
    feedbacks:
      one: 1 avaliação
      other: "%{count} avaliações"
    new_tasker: Cadastrar Prestadores
    pending: Pendente
    rejected: Rejeitado
    title: Prestadores
    # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
    verified: Verificado
    # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
    verify_failed: 'Solicitação de verificação falhou: %{message}'
    # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
    verify_success: Verificação solicitada com sucesso.
    # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
    verifying: Em Verificação
    plan:
      basic: Básico
      gold: Ouro
      diamond: Diamante
    flag:
      accepted: Quer
      allowed: Pode
      bring_products: Levar produtos
      receive_preferential_offers: Receber ofertas de preferencial
  'true': Sim
  user:
    activate: Deseja realmente ativar esse cliente? Todos os seus pedidos em pausa serão reativados.
    activate_success: Cliente ativado com sucesso.
    activate_failure: Não foi possível ativar o cliente. Ainda existem faturas não pagas.
    active: Ativo
    charge: Com taxa de cancelamento
    create_success: Cliente cadastrado com sucesso.
    debtor: Deseja realmente negativar esse cliente? Todos os seus pedidos em aberto serão pausados.
    debtor_success: Cliente negativado com sucesso.
    edit_success: Cliente atualizado com sucesso.
    free: Sem taxa de cancelamento
    indebted: Negativado
    new_user: Cadastrar Cliente
    title: Clientes
    title_new_user: Cadastro de Cliente
    titles:
      customer: Clientes
      office: Escritórios
    extra_services:
      home_assistance: Assistência Residencial
      full_assistance:  Assinatura Semanal, Plano Completo
      basic_assistance: Assinatura Quinzenal, Plano Básico
  value: Valor
  work_time: Tempo de Serviço
  zones:
    east: Leste
    north: Norte
    south: Sul
    west: Oeste
  credit_cards:
    delete_card: Você tem certeza que deseja remover este cartão?
    delete_card_success: Cartão removido com sucesso!
    delete_card_error: Não foi possível remover o cartão!
  price_source:
    manual: Manual
    internal: Interno
    external: Externo
  opportunities:
    index:
      job_number: Número
      order_number: Pedido
      location: Localização
      user_name: Cliente
      job_date: Data de Atendimento
      frequency: Frequência
      decorated_service: Serviço
      decorated_state: Status
      payouts: Valor
      taskers_names: Profissionais
  integrations:
    sendbird: Chat
    chatkit: Chat (descontinuado)
    igs: Assistência Residencial
    chat:
      true: Sim
      false: Não
