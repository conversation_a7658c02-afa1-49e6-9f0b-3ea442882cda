---
pt_BR:
  active: Ativo
  address_references_types:
    train: Trem
    subway: Metrô
    reference: Referência
  coupon:
    after_n_jobs:
      one: na %{first}ª diária
      other: na %{first}ª à %{last}ª diária
    cancellation:
      one: Cancelamento da assinatura antes de uma diária concluída tem multa de %{penalty}
      other: Cancelamento da assinatura antes de %{count} diárias concluídas tem multa de %{penalty}
    discount: Desconto de %{value}
    job_quantity:
      one: na primeira diária
      other: nas %{count} primeiras diárias
  background_check:
    unchecked: Não verificado
    checking: Verificando
    checked: Verificado
    approved: Aprovado
    not_approved: Não aprovado
    invalid_cpf: CPF inválido
    an_error_has_ocurred: Não foi possível verificar
    ativa: Ativo
    baixada: Baixado
    nula: Nulo
    suspensa: Suspenso
    inapta: Inapto
    cnpj_does_not_exist_in_receita_federal_database: CNPJ não existe na Receita Federal
  deal:
    category:
      manual_payment: Pagamento manual
      material_damage: Dano material
      others: Outros
      subscription_fee: Taxa de adesão
      working_hours: Divergência de horas trabalhadas
      morning_cancellation: Cancelamento após 10:00
      evening_cancellation: Cancelamento após 20:00
      night_cancellation: Cancelamento após 00:00
      cancellation: Cancelamento
      monthly_plan: Mensalidade
      withdraw_fee: Tarifa de saque
  duplicated: duplicado
  education:
    basic: Fundamental Completo
    graduate: Pós Graduado
    higher: Superior Completo
    middle: Segundo Grau Completo
  entry:
    admin: Administrador
    bb: Banco do Brasil
    bonus:
      eligible: Elegível
      # Wrong spelling
      elegible: Elegível
      one_time: Bônus assinatura cliente %{customer}
      favorite: Pagamento Turbinado cliente %{customer}
      # one_time: Comissão # pluton conflito
      # TODO: the 'premium_payout' feature is deprecated, but we need to support description
      #  in statement because oldest entries generated. So, in a new statemtent's new version
      #  we can drop this piece.
      premium_payout: 'Bônus diária turbo: %{job}'
      recurrency: Pagamento Turbinado cliente %{customer} # esse é melhor! mas não sei se quebra com o %{customer}
      # recurrency: Valor Extra # pluton conflito
    bradesco: Bradesco
    caixa: Caixa Econômica
    cash: Dinheiro
    check: Cheque ao Prestador
    citi: Citi Bank
    confirm_pay_tasker: Você tem certeza que deseja adicionar o valor total devido deste profissional ao próximo lote? O fechamento de lotes é feito diariamente, além de depender da liberação e saldo na Transfeera.
    create_success: Lançamento criado com sucesso.
    credit: Crédito
    debit: Débito
    deposit: Depósito bancário
    descriptions:
      cash_payout: Pagamento em dinheiro
    fee: Taxa Parafuzo
    hsbc: HSBC
    itau: Itaú
    # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
    iugu_link: Ver na Iugu
    # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
    new_transfer: Nova Transferência
    payment: Pagamento
    refund: Estornar
    refund_confirm: Você tem certeza que deseja estornar esta transação?
    refund_failed: Estorno falhou.
    refund_motives:
    - Profissional não compareceu
    - Cliente não efetuou pagamento em dinheiro
    - Erro manual
    - Erro do sistema
    refund_success: Estorno realizado com sucesso.
    refunded: Transação Estornada
    refunds: Estorno
    sales: Vendedor
    santander: Santander
    services:
      tip: Gorjeta de %{customer} em %{neighborhood} (%{date_time})
      express_cleaning:
        job_done: Diária %{customer} em %{neighborhood} (%{date_time})
      business_cleaning:
        job_done: Diária %{customer} em %{neighborhood} (%{date_time})
      cleaning:
        job_done: Diária %{customer} em %{neighborhood} (%{date_time})
      furniture_assembly:
        job_done: Montagem %{customer} em %{neighborhood} (%{date_time})
      handyman:
        job_done: Manutenção %{customer} em %{neighborhood} (%{date_time})
      heavy_cleaning:
        job_done: Limpeza pesada %{customer} em %{neighborhood} (%{date_time})
      ironing:
        job_done: Passadoria %{customer} em %{neighborhood} (%{date_time})
      painting:
        job_done: Pintura %{customer} em %{neighborhood} (%{date_time})
      pre_moving_cleaning:
        job_done: Pré-mudança %{customer} em %{neighborhood} (%{date_time})
      remodeling_cleaning:
        job_done: Pós-obra %{customer} em %{neighborhood} (%{date_time})
    show: Lançamento a %{name}
    split: Split no Cartão de Crédito
    # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
    stripe_link: Ver no Stripe
    system: Sistema
    title: Lançamentos Financeiros
    transfer: Transferência
    # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
    transfer_failed: Transferência falhou. Verifique o saldo e se esta prestadora está verificada.
    # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
    transfer_success: Transferência realizada com sucesso.
    user: Usuário
    invoice:
      cash: Valor recebido do cliente %{customer}
    deal:
      manual_payment: 'Pagamento manual %{number} (parcela %{installment})'
      material_damage: 'Dano material %{number} (parcela %{installment})'
      others: 'Acordo %{number} (parcela %{installment})'
      # TODO: the 'premium_payout' feature is deprecated, but we need to support description
      #  in statement because oldest entries generated. So, in a new statemtent's new version
      #  we can drop this piece.
      premium_payout_fee: 'Assinatura diária turbo'
      subscription_fee: 'Taxa de adesão %{number}'
      subscription_fee_installment: 'Taxa de adesão %{number} (parcela %{installment})'
      working_hours: 'Divergência de horas trabalhadas %{number} (parcela %{installment})'
      morning_cancellation: 'Cancelamento após 10:00 %{number}'
      evening_cancellation: 'Cancelamento após 20:00 %{number}'
      night_cancellation: 'Cancelamento após 00:00 %{number}'
      cancellation: 'Cancelamento %{number}'
      monthly_plan: Mensalidade %{number}
      withdraw_fee: Tarifa de saque
    descriptions:
      cash_payout: Pagamento em dinheiro
  extras:
    basic: Limpeza geral, louça
    cleanup_cabinets: Interior de armários
    cleanup_external: Limpar área externa
    cleanup_heavy: Faxina pesada
    cleanup_oven: Forno
    dish_washing: Louça
    ironing: Passar roupas
    laundry: Lavar e estender roupas
    refrigerator: Geladeira
    vaccum_carpet: Aspirar tapete e estofados
    wash_walls: Limpar paredes
    wash_windows: Janelas
  notification:
    default_title: Parafuzo
  heavy_cleaning:
    alert: Por isso, você vai ter MAIS TEMPO para concluir o serviço."
    contact: Qualquer dúvida, ligue para a Parafuzo.
    taskers: e irá trabalhar em conjunto com outro profissional que irá te ajudar.
  inactive: Descansando
  incidents:
    refund_motive: Profissional não compareceu.
  iugu_errors:
    payer:
      address:
        number: Número não é válido ou está em branco
        zip_code: CEP não é válido ou está em branco
      cpf_cnpj: CPF / CNPJ não é válido ou está em branco
  marital_status:
    divorced: Divorciado
    married: Casado
    single: Solteiro
    widower: Viúvo
  nfe:
    tax_info: 'Servicos realizados em %{month}. Quantidade: %{count} servicos.'
  offer:
    all: Todas as Diárias deste Pedido
    only_this: Somente Esta Diária
  order_item:
    each: ', cada'
    armchair:
      one: Uma poltrona
      other: "%{count} poltronas"
    armchair_type:
      breastfeeding: de amamentação
      decorative: decorativa
      recliner: reclinável
    bed:
      one: Uma cama
      other: "%{count} camas"
    bed_type:
      child: infantil
      couple: de casal
      full: de casal
      kid: infantil
      king_size: king size
      multifunctional: multifuncional
      queen: queen
      single: solteiro
      double_bed: bicama
      bunkbed: beliche
      treliche: treliche
      montessori_single: montessoriana de solteiro
      montessori_couple: montessoriana de casal
    chamber_type:
      kitchen_chamber: De cozinha
      bathroom_chamber: De banheiro
    buffet:
      one: Um buffet
      other: "%{count} buffets"
    cabinet:
      one: Um armário
      other: "%{count} armários"
    cabinet_installation: com instalação
    chair:
      one: Uma cadeira
      other: "%{count} cadeiras"
    chair_type:
      garden: para jardim
      kitchen: para cozinha
      living_room: para sala de jantar/estar
      office: para escritório
      gamer: gamer
    china_cabinet:
      one: Uma cristaleira
      other: "%{count} cristaleiras"
    closet: Armário
    cupboard: Cristaleira
    kitchen_cabinet: Armário de cozinha
    display_cabinet: Cristaleira
    dressing_table:
      one: Uma penteadeira
      other: "%{count} penteadeiras"
    cradle:
      one: Um berço
      other: "%{count} berços"
    decorative_niche:
      one: Um nicho decorativo
      other: "%{count} nichos decorativos"
    chest:
      one: Um baú
      other: "%{count} baús"
    stand:
      one: Uma prateleira
      other: "%{count} prateleiras"
    suspended_module:
      one: Um módulo aéreo
      other: "%{count} módulos aéreos"
    writing_table:
      one: Uma escrivaninha
      other: "%{count} escrivaninhas"
    coat_rack:
      one: Uma arara
      other: "%{count} araras"
    recamier:
      one: Um recamier
      other: "%{count} recamiers"
    counter:
      one: Um balcão
      other: "%{count} balcões"
    pan_cabinet:
      one: Um paneleiro
      other: "%{count} paneleiros"
    headboard:
      one: Uma cabeceira de cama
      other: "%{count} cabeceiras de cama"
    chamber:
      one: Um gabinete
      other: "%{count} gabinetes"
    doors_quantity:
      one: "%{count} porta"
      other: "%{count} portas"
    filing_cabinet:
      one: Um gaveteiro
      other: "%{count} gaveteiros"
    drawer:
      one: Um gaveteiro
      other: "%{count} gaveteiros"
    drawers_quantity:
      one: "%{count} gaveta"
      other: "%{count} gavetas"
    lockers_quantity:
      one: "%{count} gaveta"
      other: "%{count} gavetas"
    dresser:
      one: Uma cômoda
      other: "%{count} cômodas"
    dresser_type:
      adult: adulto
      kid: infantil
    fixed_on_wall: fixado na parede
    furniture:
      one: Um armário
      other: "%{count} armários"
    furniture_type:
      adult: adulto
      common: comum
      kid: infantil
      office: escritório
    is_bed: cama
    is_retractable: retrátil
    mirrors_quantity:
      one: "%{count} espelho"
      other: "%{count} espelhos"
    nightstand:
      one: Uma mesa de cabeceira
      other: "%{count} mesas de cabeceira"
    nightstand_type:
      adult: adulto
      kid: infantil
    panel_installation: com instalação
    suspended_module_installation: com instalação
    stand_installation: com instalação
    tv_stand_installation: com instalação
    decorative_niche_installation: com instalação
    headboard_installation: com instalação
    chamber_installation: com instalação
    armoire_installation: com instalação
    rack:
      one: Um rack
      other: "%{count} racks"
    seat:
      one: Um banco/banqueta
      other: "%{count} bancos/banquetas"
    tv_stand: Um rack
    shelf:
      one: Uma estante
      other: "%{count} estantes"
    bookcase: Uma estante
    shelfs_quantity:
      one: "%{count} prateleira"
      other: "%{count} prateleiras"
    shoemaker:
      one: Uma sapateira
      other: "%{count} sapateiras"
    shoe_rack: Uma sapateira
    sideboard:
      one: Um aparador
      other: "%{count} aparadores"
    sofa:
      one: Um sofá
      other: "%{count} sofás"
    sofa_type:
      common: comum
      sofa_bed: cama
    stool:
      one: Um banco/banqueta
      other: "%{count} bancos/banquetas"
    stool_type:
      garden: para jardim
      kitchen: para cozinha
      living_room: para sala de jantar/estar
      office: para escritório
      common: comum
    table:
      one: Uma mesa
      other: "%{count} mesas"
    table_type:
      garden: para jardim
      kitchen: para cozinha
      living_room: para sala de jantar/estar
      office: para escritório
      table_side: lateral
    tv_panel:
      one: Um painel de TV
      other: "%{count} painéis de TV"
    with_drawers: com gavetas
    with_mirror: com espelho
    with_panel: com painel
    with_shelf: com prateleira
    wardrobe:
      one: Um guarda-roupa
      other: "%{count} guarda-roupas"
    wardrobe_type:
      adult: adulto
      child: infantil
    armoire:
      one: Um armário
      other: "%{count} armários"
    armoire_type:
      kitchen_cabinet: de cozinha
      laundry_cabinet: de lavanderia
      office_cabinet: de escritório
      gym_locker: de academia
  phone_brand:
    android: Android
    iphone: iPhone
    other: Outro
    windows: Windows
  push:
    cancelled_task: Desculpe, a diária de %{user_name} do dia %{date} foi CANCELADA.
    confirm_preferential: O cliente %{user_name} gostou do seu serviço. Clique para saber mais detalhes.
    new_deal: 'Acordo #%{deal_number} criado, pagamento em %{installments}x de %{installment_price}'
    new_task_manual: Agendamos uma diária para você %{relative_day}, %{date} %{start_end_times} por %{payout}, cliente %{user_name}.
    reminder_preferential: Você tem seu cliente fixo %{user_name} na %{relative_day}, %{date} %{start_end_times}. Não se esqueça.
    reschedule_payout: Devido ao reagendamento de última hora pelo cliente da diária do dia %{date} você irá receber %{payout}.
    task_datetime_changed: 'A diária de %{user_name} do dia %{previous_date} mudou. NOVA DATA: %{relative_day}, %{date}, %{start_end_times}.'
    task_time_changed: 'A diária de %{user_name} do dia %{date} mudou. NOVO HORÁRIO: %{relative_day}, %{date}, %{start_end_times}.'
  search:
    accepted: Aceita
    assigned: Atribuído
    cancel: Cancelamento
    cancelled: Cancelado
    cleaning: Limpeza
    completed: Concluído
    default: Padrão
    delay: Atraso
    finished: Concluído
    invoiced: Faturado
    no_show: Falta
    on_hold: On hold
    one_use: Um Uso
    ordered: Fechado
    paid: Pago
    painting: Pintura
    paused: Pausado
    pending: Pendente
    percentage: Porcentagem
    preferential: Preferencial
    preferential_match: Convite preferencial
    pack: Pacote
    child: Pacote filha
    quoting: Orçamento
    rejected: Rejeitada
    requested: Solicitado
    reviewed: Avaliado
    scored: Votado
    skipped: Pulado
    unlimited: Ilimitado
    value: Valor
  sms:
    accept_preferential: 'Fechado! Essa diária do cliente %{user_name} é fixa %{subscription_frequency} na %{day_of_week}. Se precisar saber mais detalhes, acesse: https://pro.parafuzo.com'
    assigned_queued_task: Que pena! Outra diarista já aceitou esse serviço. Fique atenta, se esta diarista cancelar você poderá pegar este serviço. Qualquer dúvida, ligue (11)4680-3584
    assigned_task: Que pena! Outra diarista já aceitou esse serviço. Fique atenta para outras oportunidades. Qualquer dúvida, ligue (11)4680-3584
    cancelled_task: URGENTE A diária de %{user_name} do dia %{date} foi CANCELADA.
    cleanup_heavy:
      alert: Esse é um serviço de limpeza pesada. Por isso, você vai ter MAIS TEMPO para concluir o serviço
      contact: Qualquer dúvida, ligue para a Parafuzo.
      taskers: e irá trabalhar em conjunto com outro profissional que irá te ajudar
    confirm_preferential: Cliente %{user_name} gostou de você! Você pode atender um CLIENTE FIXO %{subscription_frequency} na %{relative_day} %{start_end_times}? Responda somente SIM ou NAO.
    confirm_reject: 'Entendido, você não irá mais na diária de %{relative_day}, dia %{date}. Se isso estiver incorreto, ligue imediatamente para a Parafuzo: (11)4680-3584.'
    confirm_task: Fechado! %{relative_day}, dia %{date} %{start_end_times} cliente %{user_name}.
    delayed_blocking: Oi %{tasker_name}, Você se atrasou mais uma vez. Esses atrasos podem prejudicar sua carreira. Tente evitar futuros atrasos para não começar a perder diárias. Os clientes preferem as profissionais mais comprometidas com o horário!
    delayed_warning: Oi %{tasker_name}, Vimos que você se atrasou hoje. No futuro, caso precise de ajuda com o trajeto é só ligar para a Parafuzo que a gente te ajuda, OK? Tente evitar futuros atrasos para não prejudicar a sua carreira. Os clientes preferem as profissionais mais comprometidas com o horário!
    manual_task_alert: Esta diária foi agendada especialmente para você, se não puder comparecer, diga NAO.
    multiple_taskers: Nessa diária, você trabalhará junto com %{coworkers}. Lembre-se de combinar entre vocês a divisão de tarefas.
    new_deal: 'Acordo #%{deal_number} criado, pagamento em %{installments}x de %{installment_price}'
    new_task_manual: Oi %{tasker_name}, agendamos uma diária para você %{relative_day}, %{date} %{start_end_times} por %{payout}, cliente %{user_name}.
    no_show_blocking: Oi %{tasker_name}, tudo bom? Vimos que você não foi na sua diária. Infelizmente, você vai receber menos propostas daqui pra frente, pois os clientes Parafuzo valorizam COMPROMETIMENTO. Obrigado
    no_show_warning: Oi %{tasker_name}, Que pena que você não compareceu na diária de %{relative_day}. Lembramos que os clientes sempre priorizam as profissionais mais comprometidas. Evite faltar para não prejudicar a sua carreira.
    payment_parafuzo: Para esta diária você receberá %{payout} diretamente da Parafuzo. :)
    remember_job: Oi, %{user_name}! Lembrando que você tem uma visita agendada para %{relative_day} %{date}. Para mais informações acesse https://goo.gl/Gw84UZ
    remember_job_final: Oi, %{user_name}! Lembrando que você tem uma visita agendada para %{relative_day} %{date}. Após esta visita não vamos mais te incomodar com lembretes ;)
    reminder_preferential: Você tem seu cliente fixo %{user_name} na %{relative_day}, %{date} %{start_end_times}. Não se esqueça. Se acontecer algum imprevisto, ligue para Parafuzo.
    reschedule_payout: Devido ao reagendamento de última hora pelo cliente da diária do dia %{date} você irá receber %{payout}.
    task_datetime_changed: 'URGENTE A diária de %{user_name} do dia %{previous_date} mudou. NOVA DATA: %{relative_day}, %{date}, %{start_end_times}. Responda NAO se não puder comparecer.'
    task_time_changed: 'URGENTE A diária de %{user_name} do dia %{date} mudou. NOVO HORARIO: %{relative_day}, %{date}, %{start_end_times}. Responda NAO para CANCELAR.'
    tasker_delayed: Olá %{user_name}, identificamos que a profissional %{tasker_name} que irá te atender hoje as %{start_time} está %{delay_time} minutos atrasada. Este é um aviso automático.
    wrong_response: Não conseguimos identificar sua mensagem. Se precisar, por favor ligue para nós.
  sms_format:
    branding: PARAFUZO %{message}
    paginate: "%{page}/%{total} %{message}"
    taskers:
      one: 1 profissional
      other: "%{count} profissionais"
  stripe:
    description: "%{full_name}"
  subscription_type:
    single: apenas uma vez
    once_week: toda semana
    once_2_weeks: toda quinzena
    once_month: todo mês
