pt_BR:
  mongoid:
    errors:
      messages:
        blank_in_locale: "não pode ser branco em %{location}"
        message_title: "mensagem"
        summary_title: "resumo"
        resolution_title: "resolução"

        ambiguous_relationship:
          message: Ambiguous relations %{candidates} defined on %{klass}.
          resolution: On the %{name} relation on %{inverse} you must add an :inverse_of option to specify the exact relationship on %{klass} that is the opposite of %{name}.
          summary: 'When Mongoid attempts to set an inverse document of a relation in memory, it needs to know which relation it belongs to. When setting %{name}, Mongo<PERSON> looked on the class %{inverse} for a matching relation, but multiples were found that could potentially match: %{candidates}.'
        blank: deve ser preenchido
        blank_in_locale: can't be blank in %{location}
        callbacks:
          message: Calling %{method} on %{klass} resulted in a false return from a callback.
          resolution: Double check all before callbacks to make sure they are not unintentionally returning false.
          summary: If a before callback returns false when using Document.create!, Document#save!, or Documnet#update_attributes! this error will get raised since the document did not actually get saved.
        calling_document_find_with_nil_is_invalid:
          message: Calling Document.find with nil is invalid.
          resolution: Most likely this is caused by passing parameters directly through to the find, and the parameter either is not present or the key from which it is accessed is incorrect.
          summary: Document.find expects the parameters to be 1 or more ids, and will return a single document if 1 id is provided, otherwise an array of documents if multiple ids are provided.
        delete_restriction:
          message: Cannot delete %{document} because of dependent '%{relation}'.
          resolution: Don't attempt to delete the parent %{document} when it has children, or change the dependent option on the relation.
          summary: When defining '%{relation}' with a :dependent => :restrict, Mongoid will raise an error when attempting to delete the %{document} when the child '%{relation}' still has documents in it.
        invalid_document: informado não é valido
        document_not_destroyed:
          message: "%{klass} with id %{id} was not destroyed."
          resolution: Check the before/after destroy callbacks to ensure that the return values are truthy for the chain to continue.
          summary: When calling %{klass}#destroy! and a callback halts the destroy callback chain by returning a false value, the deletion will not actually occur.
        document_not_found:
          message: Documento não encontrado para %{klass} com o id %{missing}.
          resolution: Search for an id that is in the database or set the Mongoid.raise_not_found_error configuration option to false, which will cause a nil to be returned instead of raising this error when searching for a single id, or only the matched documents when searching for multiples.
          summary: 'When calling %{klass}.find with an id or array of ids, each parameter must match a document in the database or this error will be raised. The search was for the id(s): %{searched} (%{total} total) and the following ids were not found: %{missing}.'
        document_with_attributes_not_found:
          message: Documento não encontrado para %{klass} com os atributos %{attributes}.
          resolution: Search for attributes that are in the database or set the Mongoid.raise_not_found_error configuration option to false, which will cause a nil to be returned instead of raising this error.
          summary: When calling %{klass}.find_by with a hash of attributes, all attributes provided must match a document in the database or this error will be raised.
        eager_load:
          message: Eager loading :%{name} is not supported since it is a polymorphic belongs_to relation.
          resolution: Don't attempt to perform this action and have patience, maybe this will be supported in the future.
          summary: Mongoid cannot currently determine the classes it needs to eager load when the relation is polymorphic. The parents reside in different collections so a simple id lookup is not sufficient enough.
        invalid_collection:
          message: Access to the collection for %{klass} is not allowed.
          resolution: For access to the collection that the embedded document is in, use %{klass}#_root.collection, or do not attempt to persist an embedded document without a parent set.
          summary: "%{klass}.collection was called, and %{klass} is an embedded document - it resides within the collection of the root document of the hierarchy."
        invalid_config_option:
          message: 'Invalid configuration option: %{name}.'
          resolution: |-
            Remove the invalid option or fix the typo. If you were expecting the option to be there, please consult the following page with repect to Mongoid's configuration:

               http://mongoid.org/en/mongoid/docs/installation.html
          summary: 'A invalid configuration option was provided in your mongoid.yml, or a typo is potentially present. The valid configuration options are: %{options}.'
        invalid_field:
          message: Defining a field named '%{name}' is not allowed.
          resolution: Use Mongoid.destructive_fields to see what names are not allowed, and don't use these names. These include names that also conflict with core Ruby methods on Object, Module, Enumerable, or included gems that inject methods into these or Mongoid internals.
          summary: |-
            Defining this field would override the method '%{name}', which would cause issues with expectations around the original method and cause extremely hard to debug issues. The original method was defined in:
               Object: %{origin}
               File: %{file}
               Line: %{line}
        invalid_field_option:
          message: Invalid option :%{option} provided for field :%{name}.
          resolution: |+
            When defining the field :%{name} on '%{klass}', please provide valid options for the field. These are currently: %{valid}. If you meant to define a custom field option, please do so first like so:

               Mongoid::Fields.option :%{option} do |model, field, value|
                 # Your logic here...
               end
               class %{klass}
                 include Mongoid::Document
                 field :%{name}, %{option}: true
               end

          summary: Mongoid requires that you only provide valid options on each field definition in order to prevent unexpected behaviour later on.
        invalid_includes:
          message: 'Invalid includes directive: %{klass}.includes(%{args})'
          resolution: 'Ensure that each parameter passed to %{klass}.includes is a valid name of a relation on the %{klass} model. These are: %{relations}.'
          summary: Eager loading in Mongoid only supports providing arguments to %{klass}.includes that are the names of relations on the %{klass} model, and only supports one level of eager loading. (ie, eager loading associations not on the %{klass} but one step away via another relation is not allowed.
        invalid_index:
          message: 'Invalid index specification on %{klass}: %{spec}, %{options}'
          resolution: |+
            Ensure that the index conforms to the correct syntax and has the correct options.

             Valid options are:
               background: true|false
               database: 'database_name'
               drop_dups: true|false
               name: 'index_name'
               sparse: true|false
               unique: true|false
               min: 1
               max: 1
               bits: 26
               bucket_size : 1
               weights: { content: 1, title: 2 }
               expire_after_seconds: number_of_seconds
             Valid types are: 1, -1, '2d', '2dsphere', 'geoHaystack', 'text', 'hashed'

             Example:
               class Band
                 include Mongoid::Document
                 index({ name: 1, label: -1 }, { sparse: true })
                 index({ location: '2d' }, { background: true })
               end

          summary: Indexes in Mongoid are defined as a hash of field name and direction/2d pairs, with a hash for any additional options.
        invalid_options:
          message: Invalid option :%{invalid} provided to relation :%{name}.
          resolution: 'Valid options are: %{valid}, make sure these are the ones you are using.'
          summary: Mongoid checks the options that are passed to the relation macros to ensure that no ill side effects occur by letting something slip by.
        invalid_path:
          message: Having a root path assigned for %{klass} is invalid.
          resolution: Most likely your embedded model, %{klass} is also referenced via a has_many from a root document in another collection. Double check the relation definitions and fix any instances where embedded documents are improperly referenced from other collections.
          summary: Mongoid has two different path objects for determining the location of a document in the database, Root and Embedded. This error is raised when an embedded document somehow gets a root path assigned.
        invalid_scope:
          message: Defining a scope of value %{value} on %{klass} is not allowed.
          resolution: |+
            Change the scope to be a proc wrapped critera.

             Example:
               class Band
                 include Mongoid::Document
                 scope :inactive, ->{ where(active: false) }
               end

          summary: Scopes in Mongoid must be procs that wrap criteria objects.
        invalid_set_polymorphic_relation:
          message: The %{name} attribute can't be set to an instance of %{other_klass} as %{other_klass} has multiple relations referencing %{klass} as %{name}.
          resolution: Set the values from the parent, or redefine the relation with only a single definition in the parent.
          summary: If the parent class of a polymorphic relation has multiple definitions for the same relation, the values must be set from the parent side and not the child side since Mongoid cannot determine from the child side which relation to go in.
        invalid_storage_options:
          message: 'Invalid options passed to %{klass}.store_in: %{options}.'
          resolution: |+
            Change the options passed to store_in to match the documented API, and ensure all keys in the options hash are symbols.

             Example:
               class Band
                 include Mongoid::Document
                 store_in collection: 'artists', database: 'secondary'
               end

          summary: The :store_in macro takes only a hash of parameters with the keys :database, :collection, or :session.
        invalid_storage_parent:
          message: Invalid store_in call on class %{klass}.
          resolution: Remove the store_in call on class %{klass}, as it will use its parent store configuration. Or remove the hierarchy extension for this class.
          summary: The :store_in macro can only be called on a base Mongoid Document
        invalid_time:
          message: "'%{value}' is not a valid Time."
          resolution: Make sure to pass parsable values to the field setter for Date, DateTime, and Time objects. When this is a String it needs to be valid for Time.parse. Other objects must be valid to pass to Time.local.
          summary: Mongoid tries to serialize the values for Date, DateTime, and Time into proper UTC times to store in the database. The provided value could not be parsed.
        invalid_value:
          message: Value of type %{value_class} cannot be written to a field of type %{field_class}
          resolution: Verify if the value to be set correspond to field definition
          summary: Tried to set a value of type %{value_class} to a field of type %{field_class}
        inverse_not_found:
          message: When adding a(n) %{klass} to %{base}#%{name}, Mongoid could not determine the inverse foreign key to set. The attempted key was '%{inverse}'.
          resolution: |-
            If an inverse is not required, like a belongs_to or has_and_belongs_to_many, ensure that :inverse_of => nil is set on the relation. If the inverse is needed, most likely the inverse cannot be figured out from the names of the relations and you will need to explicitly tell Mongoid on the relation what the inverse is.

             Example:
               class Lush
                 include Mongoid::Document
                 has_one :whiskey, class_name: "Drink", inverse_of: :alcoholic
               end

               class Drink
                 include Mongoid::Document
                 belongs_to :alcoholic, class_name: "Lush", inverse_of: :whiskey
               end
          summary: When adding a document to a relation, Mongoid attempts to link the newly added document to the base of the relation in memory, as well as set the foreign key to link them on the database side. In this case Mongoid could not determine what the inverse foreign key was.
        mixed_relations:
          message: Referencing a(n) %{embedded} document from the %{root} document via a relational association is not allowed since the %{embedded} is embedded.
          resolution: Consider not embedding %{embedded}, or do the key storage and access in a custom manner in the application code.
          summary: In order to properly access a(n) %{embedded} from %{root} the reference would need to go through the root document of %{embedded}. In a simple case this would require Mongoid to store an extra foreign key for the root, in more complex cases where %{embedded} is multiple levels deep a key would need to be stored for each parent up the hierarchy.
        mixed_session_configuration:
          message: 'Both uri and standard configuration options defined for session: ''%{name}''.'
          resolution: Provide either only a uri as configuration or only standard options.
          summary: 'Instead of simply giving uri or standard options a preference order, Mongoid assumes that you have made a mistake in your configuration and requires that you provide one or the other, but not both. The options that were provided were: %{config}.'
        nested_attributes_metadata_not_found:
          message: 'Could not find metadata for relation ''%{name}'' on model: %{klass}.'
          resolution: |+
            Make sure that there is a relation defined named '%{name}' on %{klass} or that the relation definition comes before the accepts_nested_attributes_for macro in the model - order matters so that Mongoid has access to the metadata.

             Example:
               class Band
                 include Mongoid::Document
                 has_many :albums
                 accepts_nested_attributes_for :albums
               end

          summary: When defining nested attributes for a relation, Mongoid needs to access the metadata for the relation '%{name}' in order if add autosave functionality to it, if applicable. Either no relation named '%{name}' could be found, or the relation had not been defined yet.
        no_default_session:
          message: No default session configuration is defined.
          resolution: |+
            If configuring via a mongoid.yml, ensure that within your :sessions section a :default session is defined.

             Example:
               development:
                 sessions:
                   default:
                     hosts:
                       - localhost:27017

          summary: 'The configuration provided settings for: %{keys}, but Mongoid requires a :default to be defined at minimum.'
        no_environment:
          message: Could not load the configuration since no environment was defined.
          resolution: Make sure some environment is set from the mentioned options. Mongoid cannot load configuration from the yaml without knowing which environment it is in, and we have considered defaulting to development an undesireable side effect of this not being defined.
          summary: Mongoid attempted to find the appropriate environment but no Rails.env, Sinatra::Base.environment, RACK_ENV, or MONGOID_ENV could be found.
        no_main: principal não escolhido.
        no_map_reduce_output:
          message: No output location was specified for the map/reduce operation.
          resolution: |+
            Provide the location that the output of the operation is to go by chaining an #out call to the map/reduce.

             Example:
               Band.map_reduce(map, reduce).out(inline: 1)

             Valid options for the out function are:
               inline:  1
               merge:   'collection-name'
               replace: 'collection-name'
               reduce:  'collection-name'

          summary: 'When executing a map/reduce, you must provide the output location of the results. The attempted command was: %{command}.'
        no_metadata:
          message: Metadata not found for document of type %{klass}.
          resolution: Ensure that your relations on the %{klass} model are all properly defined, and that the inverse relations are also properly defined. Embedded relations must have both the parent (embeds_one/embeds_many) and the inverse (embedded_in) present in order to work properly.
          summary: Mongoid sets the metadata of a relation on the document when it is either loaded from within the relation, or added to one. The presence of the metadata is required in order to provide various functionality around relations. Most likely you are getting this error because the document is embedded and was attempted to be persisted without being associated with a parent, or the relation was not properly defined.
        no_parent:
          message: Cannot persist embedded document %{klass} without a parent document.
          resolution: Ensure that you've set the parent relation if instantiating the embedded document directly, or always create new embedded documents via the parent relation.
          summary: If the document is embedded, in order to be persisted it must always have a reference to its parent document. This is most likely caused by either calling %{klass}.create or %{klass}.create! without setting the parent document as an attribute.
        no_session_config:
          message: No configuration could be found for a session named '%{name}'.
          resolution: Double check your mongoid.yml to make sure under the sessions key that a configuration exists for '%{name}'. If you have set the configuration programatically, ensure that '%{name}' exists in the configuration hash.
          summary: 'When attempting to create the new session, Mongoid could not find a session configuration for the name: ''%{name}''. This is necessary in order to know the host, port, and options needed to connect.'
        no_session_database:
          message: 'No database provided for session configuration: :%{name}.'
          resolution: |+
            If configuring via a mongoid.yml, ensure that within your :%{name} section a :database value for the session's default database is defined.

             Example:
               development:
                 sessions:
                   %{name}:
                     database: my_app_db
                     hosts:
                       - localhost:27017

          summary: 'Each session configuration must provide a database so Mongoid knows where the default database to persist to. What was provided was: %{config}.'
        no_session_hosts:
          message: 'No hosts provided for session configuration: :%{name}.'
          resolution: |+
            If configuring via a mongoid.yml, ensure that within your :%{name} section a :hosts value for the session hosts is defined.

             Example:
               development:
                 sessions:
                   %{name}:
                     database: my_app_db
                     hosts:
                       - localhost:27017

          summary: 'Each session configuration must provide hosts so Mongoid knows where the database server is located. What was provided was: %{config}.'
        no_sessions_config:
          message: No sessions configuration provided.
          resolution: |+
            Double check your mongoid.yml to make sure that you have a top-level sessions key with at least 1 default session configuration for it. You can regenerate a new mongoid.yml for assistance via `rails g mongoid:config`.

             Example:
               development:
                 sessions:
                   default:
                     database: mongoid_dev
                     hosts:
                       - localhost:27017

          summary: Mongoid's configuration requires that you provide detail about each session that can be connected to, and requires in the sessions config at least 1 default session to exist.
        readonly_attribute:
          message: 'Attempted to set the readonly attribute ''%{name}'' with the value: %{value}.'
          resolution: Don't define '%{name}' as readonly, or do not attempt to update its value after the document is persisted.
          summary: Attributes flagged as readonly via Model.attr_readonly can only have values set when the document is a new record.
        readonly_document:
          message: Attempted to persist the readonly document '%{klass}'.
          resolution: Don't attempt to persist documents that are flagged as readonly.
          summary: 'Documents loaded from the database using #only cannot be persisted.'
        scope_overwrite:
          message: Cannot create scope :%{scope_name}, because of existing method %{model_name}.%{scope_name}.
          resolution: Change the name of the scope so it does not conflict with the already defined method %{model_name}, or set the configuration option Mongoid.scope_overwrite_exception to false, which is its default. In this case a warning will be logged.
          summary: When defining a scope that conflicts with a method that already exists on the model, this error will get raised if Mongoid.scope_overwrite_exception is set to true.
        taken: Já existe um registro com esse valor
        tasker_unavailable: Prestador já tem um job para este dia
        too_many_nested_attribute_records:
          message: Accepting nested attributes for %{association} is limited to %{limit} records.
          resolution: 'The limit is set as an option to the macro, for example: accepts_nested_attributes_for :%{association}, limit: %{limit}. Consider raising this limit or making sure no more are sent than the set value.'
          summary: More documents were sent to be processed than the allowed limit.
        unknown_attribute:
          message: Attempted to set a value for '%{name}' which is not allowed on the model %{klass}.
          resolution: You can include Mongoid::Attributes::Dynamic if you expect to be writing values for undefined fields often.
          summary: Without including Mongoid::Attributes::Dynamic in your model and the attribute does not already exist in the attributes hash, attempting to call %{klass}#%{name}= for it is not allowed. This is also triggered by passing the attribute to any method that accepts an attributes hash, and is raised instead of getting a NoMethodError.
        unsaved_document:
          message: Attempted to save %{document} before the parent %{base}.
          resolution: Make sure to only use create or create! when the parent document %{base} is persisted.
          summary: You cannot call create or create! through the relation (%{document}) who's parent (%{base}) is not already saved. This would case the database to be out of sync since the child could potentially reference a nonexistant parent.
        unsupported_javascript:
          message: Executing Javascript $where selector on an embedded criteria is not supported.
          resolution: 'Please provide a standard hash to #where when the criteria is for an embedded relation.'
          summary: 'Mongoid only supports providing a hash of arguments to #where criterion on embedded documents. Since %{klass} is embedded, the expression %{javascript} is not allowed.'
        validations:
          message: Validação de %{document} falhou.
          resolution: deixe os dados válidos ou remova as validações.
          summary: 'Erro encontrado: %{errors}'
        wrong_tasker_service: Prestador não sabe executar este serviço
      models:
        tasker_feedback:
          attributes:
            job:
              not_finished: não finalizado. Profissional só pode avaliar serviços concluídos.
            score:
              already_scored: já registrada
            state:
              expired: expirado
        address:
          attributes:
            cep:
              blocked: bloqueado
        coupon:
          attributes:
            code:
              invalid: Código inválido
        job:
          attributes:
            date:
              bill: Somente a partir de %{date}
              order_paused: Este serviço não pode ser agendado pois sua assinatura está pausada
              used: Já existe um serviço nesta data (%{date} - %{number})
            job_taskers:
              invalid: Slot inválido
              unavailable: Prestador já tem um job para este dia
            number:
              diverge: do job diverge do pedido
            service:
              invalid: inválido.
            user:
              invalid: Inválido.
        job_tasker:
          attributes:
            tasker:
              unavailable: Prestador já tem um job para este dia
        order:
          attributes:
            address:
              invalid: inválido.
            date:
              taken: já existe um pedido nesta data
              overlap_conflict: data conflitante com outra assinatura
            jobs:
              invalid: job inválido
            region:
              unavaiable: não está disponível
            service:
              unchangeable: não pode ser mudado
            user:
              indebted: Usuário não pode criar pedido sem um cartão novo.
              invalid: Inválido.
              unchangeable: não pode ser mudado
            order_taskers:
              blocked_tasker: preferencial está bloqueada neste pedido
            base_week:
              invalid_element: semana base incompatível com a frequência da assinatura
        user:
          attributes:
            addresses:
              invalid: incompleto
            cpf:
             already_used: 'já cadastrado com email %{email}'
