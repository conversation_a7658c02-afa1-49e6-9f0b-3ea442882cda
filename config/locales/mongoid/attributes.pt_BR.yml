pt_BR:
  mongoid:
    attributes:
      incident:
        type: Tipo
        type/cancel: Cancelamento
        type/delayed: Atraso
        type/no_show: Falta
        severity: Criticidade
        severity/normal: Normal
        severity/high: Alta
      job:
        number: Número
        service: Serviço
        user: Cliente
      offer:
        type/default: Padrão
        type/preferential: Preferencial
        type/preferential_match: Convite preferencial
        type/pack: Pacote
        type/child: Filha
      order:
        address: Endereço
        addresses: endereço
        date: Data
        region: Região
        service: Serviço
        user: Cliente
        user_id: Cliente
        partner_order_id: ID do pedido (parceiro)
      tasker:
        accepts_payout: Opções de Pagamento
        active: Situação
        address: Endereço
        availability: Disponibilidade
        bank: Banco
        banks: Banco
        birthdate: Data de nascimento
        cpf: CPF
        education: Escolaridade
        gender: Sexo/Gênero
        has_children: Filhos?
        indication: Indicação
        interest_address: Endereço de interesse
        marital_status: Estado civil
        mei: MEI
        mei_password: Senha
        notes: Observações
        notify_type: Tipo de notificação
        password: Senha
        pets_allowed: Aceita jobs com pets?
        payable_by_cash: Pagamento em dinheiro
        # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
        payment_frequency: Frequência de pagamento
        political_exposure: Pessoa politicamente exposta
        phone: Telefone
        phone_brand: Celular
        plan: Plano
        rg: RG
        score: Nota do Prestador
        services: Serviços
        service_ids: Serviços
        state/disabled: Inativo
        state/enabled: Ativo
        state/lead: Lead
        state/onboarding: Onboarding
        state/suspended: Suspenso
        state: Situação
        test_score: Nota da prova
        zones: Zona de atuação
      user:
        addresses: endereço
      tasker_feedback:
        score: nota
        questions: perguntas
        state/created: aguardando
        state/expired: vencido
        state/scored: respondido
      tasker_feedback_question:
        arrival_time: 'horário de chegada'
        was_client_home: 'cliente em casa'
        was_enough_hours: 'horas suficientes'
        was_enough_hours/sufficient: suficiente
        was_enough_hours/insufficient: insuficiente
        was_enough_hours/excessive: excessivo
        was_respected: 'foi respeitado'
        work_again: 'trabalharia novamente'
