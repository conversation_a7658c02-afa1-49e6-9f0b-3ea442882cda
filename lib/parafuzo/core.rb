# frozen_string_literal: true

require 'active_support'
require 'resque'
require 'resque-scheduler'
require 'resque_solo'
require 'i18n'
require 'geocoder'
require 'i18n/backend/fallbacks'
require 'holidays'
require 'business_time'
require 'aws-sdk-s3'
require 'action_view/helpers'
require 'iugu/marketplace'
require 'stripe'
require_relative '../cep_search'
require 'nfe'
require 'rollout'

I18n.load_path += Dir[File.expand_path('../../config/locales/**/*.yml', __dir__)]
I18n.default_locale = I18n.locale = :pt_BR

module Parafuzo
  autoload :CepSearch, 'parafuzo/cep_search'

  module Core
    autoload :Block, 'core/block'
    autoload :Job, 'core/job'
    autoload :Transaction, 'core/transaction'
    autoload :Location, 'core/location'
    autoload :Queue, 'core/queue'
  end
end

autoload :AntecedenceLimits, 'antecedence_limits'

module Descriptors
  autoload :Base, 'descriptors/base'
  autoload :Coupon, 'descriptors/coupon'
  autoload :Deal, 'descriptors/deal'
  autoload :Entry, 'descriptors/entry'
  autoload :OrderItem, 'descriptors/order_item'
end

module Payments
  autoload :Base, 'payments/base'
  autoload :IuguBase, 'payments/iugu_base'
  autoload :Gateway, 'payments/gateway'
  autoload :Response, 'payments/response'
  autoload :Expire, 'payments/expire'
  autoload :Capture, 'payments/capture'
  autoload :Refund, 'payments/refund'
  autoload :StripeBase, 'payments/stripe_base'
  autoload :Cancel, 'payments/cancel'
  autoload :Charge, 'payments/charge'

  autoload :DefaultDecorator, 'payments/decorators/default'
  autoload :InvoiceDecorator, 'payments/decorators/invoice'

  module CreditCard
    autoload :Stripe, 'payments/credit_card/stripe'
  end

  module Bill
    autoload :Iugu, 'payments/bill/iugu'
    autoload :Stripe, 'payments/bill/stripe'
  end

  module CardInvoice
    autoload :Iugu, 'payments/card_invoice/iugu'
  end

  module Pix
    autoload :Transfeera, 'payments/pix/transfeera'
  end

  module Refunds
    autoload :Iugu, 'payments/refunds/iugu'
    autoload :Stripe, 'payments/refunds/stripe'
    autoload :Transfeera, 'payments/refunds/transfeera'
  end

  module Cancels
    autoload :Iugu, 'payments/cancels/iugu'
  end
end

autoload :InvoiceService,  'invoice/service'
autoload :InvoiceCharge,   'invoice/charge'
autoload :InvoiceNotifier, 'invoice/notifier'
autoload :InvoicePacker,   'invoice/packer'
autoload :InvoiceCapture,  'invoice/capture'
autoload :InvoiceRefunder, 'invoice/refunder'

autoload :OrderSetRegion, 'factories/order/set_region'

autoload :OrderService, 'services/order'
class OrderService
  autoload :AvailableFavoriteTaskers, 'services/order/available_favorite_taskers'
  autoload :AvailablePreferentialTaskers, 'services/order/available_preferential_taskers'
  autoload :Subscription, 'services/order/subscription'
  autoload :Preferential, 'services/order/preferential'
  autoload :UserAddress, 'services/order/user_address'
  autoload :ChangeAddress, 'services/order/change_address'
  autoload :Conflict, 'services/order/conflict'
end

autoload :BlockerService, 'services/blocker'
autoload :BonusService, 'services/bonus'
autoload :CancelSubscriptionService, 'services/cancel_subscription'
autoload :ChangeJobService, 'services/change_job'
autoload :ChangeOrderService, 'services/change_order'
autoload :CheckoutService, 'services/checkout'
autoload :CouponService, 'services/coupon'
autoload :CreditCardService, 'services/credit_card'
autoload :DealService, 'services/deal'
autoload :EntryService, 'services/entry'
autoload :EventService, 'services/event'
autoload :FeedbackService, 'services/feedback'
class FeedbackService
  autoload :ComputeFeedback, 'services/compute_feedback'
end
autoload :GeoSupportService, 'services/geo_support'
autoload :HotSiteService, 'services/hotsite'
autoload :IncidentService, 'services/incident'
autoload :JobChargeService, 'services/job_charge'
autoload :JobOffersService, 'services/job_offers'
autoload :JobService, 'services/job'
autoload :NfeCalculationService, 'services/nfe_calculation'
autoload :OrderUserService, 'services/order_user'
autoload :PaymentService, 'services/payment'
autoload :PaymentService, 'services/payment'
autoload :PhotoService, 'services/photo'
autoload :PreferentialReplaceService, 'services/preferential_replace'
autoload :PricingService, 'services/pricing'
autoload :S3Service, 'services/s3'

# Module created to flag deprecated services that will soon be replaced
#
module Deprecated
  autoload :RescheduleService, 'services/deprecated/reschedule'
  autoload :SkipService, 'services/deprecated/skip'
end
autoload :TaskerService, 'services/tasker'
autoload :TaskerFeedbackService, 'services/tasker_feedback'
autoload :UnpaidJobsService, 'services/unpaid_jobs'
autoload :UserService, 'services/user'

module Taskers
  autoload :Rollout, 'services/taskers/rollout'
  autoload :Calendar, 'services/taskers/calendar'
  autoload :AvailableDays, 'services/taskers/available_days'
  autoload :ServicesManager, 'services/taskers/services_manager'
end

autoload :OrderWatcher, 'services/order_watcher'
autoload :AssignWatcher, 'services/assign_watcher'

autoload :DateTimeValidatorHelpers, 'services/coupon_validator/date_time'

module CouponValidator
  autoload :Base, 'services/coupon_validator/base'
  autoload :Job, 'services/coupon_validator/job'
  autoload :OnePerCustomer, 'services/coupon_validator/one_per_customer'
  autoload :OneUse, 'services/coupon_validator/one_use'
  autoload :Simple, 'services/coupon_validator/simple'
  autoload :Subscription, 'services/coupon_validator/subscription'
  autoload :Single, 'services/coupon_validator/single'
end

module Gateways
  autoload :Iugu, 'payments/gateways/iugu'
  autoload :Stripe, 'payments/gateways/stripe'
  autoload :Transfeera, 'payments/gateways/transfeera'
end

autoload :OrderFactory, 'factories/order'
autoload :JobFactory, 'factories/job'
autoload :PaymentFactory, 'factories/payment'
autoload :NextDate, 'factories/next_date'
autoload :DealFactory, 'factories/deal'
autoload :JobPackFactory, 'factories/job_pack'

autoload :Pricing, 'pricing/pricing'

module FindTasker
  autoload :Base, 'find_tasker/base'
  autoload :BaseSearch, 'find_tasker/base_search'
  autoload :Config, 'find_tasker/config'
  autoload :Processor, 'find_tasker/processor'
  autoload :ListFactory, 'find_tasker/list_factory'
  autoload :Collection, 'find_tasker/collection'
  autoload :Item, 'find_tasker/item'
  autoload :Preferential, 'find_tasker/preferential'
  autoload :PreferentialMatch, 'find_tasker/preferential_match'
  class Preferential
    autoload :SendOffers, 'find_tasker/preferential/send_offers'
  end
  autoload :Notifier, 'find_tasker/notifier'
  autoload :OfferParameters, 'find_tasker/offer_parameters'
  autoload :PackSearch, 'find_tasker/pack_search'
  autoload :Pack, 'find_tasker/pack'
  autoload :AvailableJobs, 'find_tasker/available_jobs'
end

autoload :BaseNotifier, 'notifiers/base'
autoload :PaymentNotifier, 'notifiers/payment'
autoload :JobPaymentNotifier, 'notifiers/job_payment'
autoload :OrderNotifier, 'notifiers/order'
autoload :OfferNotifier, 'notifiers/offer'
autoload :JobNotifier, 'notifiers/job'

module Messenger
  module Writers
    autoload :Base, 'messengers/writers/base'
    autoload :Default, 'messengers/writers/default'
    autoload :Cleaning, 'messengers/writers/cleaning'
    autoload :Payment, 'messengers/writers/payment'
  end

  autoload :Base, 'messengers/base'
  autoload :Writer, 'messengers/writer'
  autoload :Notification, 'messengers/notification'
  autoload :Processor, 'messengers/processor'
  autoload :Push, 'messengers/push'
  autoload :Logger, 'messengers/logger'
  autoload :Router, 'messengers/router'
end

module Workers
  autoload :Base, 'workers/base'
  autoload :Charge, 'workers/charge'
  autoload :Chat, 'workers/chat'
  autoload :BuildNextJobs, 'workers/build_next_jobs'
  autoload :Stripe, 'workers/stripe'
  autoload :NfeDispatcher, 'workers/nfe_dispatcher'
  autoload :NfeIssuer, 'workers/nfe_issuer'
  autoload :PreferentialDispatcher, 'workers/preferential_dispatcher'
  autoload :NextJobs, 'workers/next_jobs'
  autoload :JobFinished, 'workers/job_finished'
  autoload :Notify, 'workers/notify'
  autoload :EmailEvent, 'workers/email_event'
  autoload :IuguEvent, 'workers/iugu_event'
  autoload :InvoiceChecker, 'workers/invoice_checker'
  autoload :InvoicePackerBase, 'workers/invoice_packer_base'
  autoload :InvoicePackerCompany, 'workers/invoice_packer_company'
  autoload :InvoicePackerOrder, 'workers/invoice_packer_order'
  autoload :FindPreferential, 'workers/find_preferential'
  autoload :FindPreferentialMatch, 'workers/find_preferential_match'
  autoload :ActivateUser, 'workers/user_activate'
  autoload :DebtorUser, 'workers/user_debtor'
  autoload :FindRelatedEntries, 'workers/find_related_entries'
  autoload :CancelPendingJobs, 'workers/cancel_pending_jobs'
  autoload :OrderGeocoder, 'workers/order_geocoder'
  autoload :ExpireTaskerFeedback, 'workers/expire_tasker_feedback'
  autoload :TaskerFeedbackRequest, 'workers/tasker_feedback_request'
  autoload :InvoiceTipCreator, 'workers/invoice_tip_creator'
  autoload :PayableByCashTaskerUpdater, 'workers/payable_by_cash_tasker_updater'
  autoload :PaymentAccountCheckUpdater, 'workers/payment_account_check_updater'
  autoload :PaymentAccountChecker, 'workers/payment_account_checker'
  autoload :PaymentBatchCreator, 'workers/payment_batch_creator'
  autoload :PaymentStatusUpdater, 'workers/payment_status_updater'
  autoload :PaymentBatchNameUpdater, 'workers/payment_batch_name_updater'
  autoload :HomeMaintenance, 'workers/home_maintenance'
  autoload :TaskerHistory, 'workers/tasker_history'
  autoload :BackgroundCheck, 'workers/background_check'
  autoload :MeiCheck, 'workers/mei_check'
  autoload :JobMarkPixCompletedAsPaid, 'workers/job_mark_pix_completed_as_paid'
  autoload :Feedback, 'workers/feedback'
  autoload :PendingOffers, 'workers/pending_offers'

  # jobs and invoices charges/captures/credits
  #
  autoload :InvoiceCharge, 'workers/invoice_charge'
  autoload :RetryCaptureInvoices, 'workers/retry_capture_invoices'

  autoload :JobCharge, 'workers/job_charge'
  autoload :ProcessInvoicesCredits, 'workers/process_invoices_credits'
  autoload :ProcessCapturablePayments, 'workers/process_capturable_payments'
end

autoload :TaskerFeedbackDecoratorable, 'decorators/tasker_feedback_decoratorable'

module DW
  autoload :BaseDecorator, 'decorators/dw/base'
  autoload :IncidentDecorator, 'decorators/dw/incident'
  autoload :JobDecorator, 'decorators/dw/job'
  autoload :OrderDecorator, 'decorators/dw/order'
  autoload :EntryDecorator, 'decorators/dw/entry'
  autoload :JobPackDecorator, 'decorators/dw/job_pack'
  autoload :AddressReferenceDecorator, 'decorators/dw/address_reference'
  autoload :TaskerFeedbackDecorator, 'decorators/dw/tasker_feedback'
  autoload :TaskerFeedbackQuestionDecorator, 'decorators/dw/tasker_feedback_question'
end

module ServiceTypes
  # :nodoc:
  module FurnitureAssembly
    autoload :ServiceItems, 'service_types/furniture_assembly'
  end
end

autoload :FormatHelper, 'helpers/format_helper'
