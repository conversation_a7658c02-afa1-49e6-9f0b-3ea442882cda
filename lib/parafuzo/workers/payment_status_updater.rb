# frozen_string_literal: true

require_relative '../services/payout/payment_status_updater'

module Workers
  # Updates the status of the entries received from Payments service through the payments-on-status-update pub/sub
  # topic.
  #
  class PaymentStatusUpdater < Base
    include Resque::Plugins::UniqueJob

    # The subscription to pull.
    #
    SUBSCRIPTION_NAME = 'payments-on-status-update-poseidon'

    # The maximum number of messages to pull in each call to Pub/Sub.
    #
    MAX_MESSAGES = ENV.fetch('PAYMENT_STATUS_UPDATER_MAX_MESSAGES', 128).to_i

    # Re<PERSON> will run using this queue name.
    #
    @queue = :payment

    # It'll be called by resque to run the job.
    #
    def run
      loop do
        response = Parafuzo::Core::Queue.pull(SUBSCRIPTION_NAME, MAX_MESSAGES)

        break if response.received_messages.blank?

        ack_ids = response.received_messages.map do |received_message|
          Services::Payout::PaymentStatusUpdater.process JSON.parse(received_message.message.data)

          received_message.ack_id
        end
        Parafuzo::Core::Queue.ack(SUBSCRIPTION_NAME, ack_ids)
      end
    end
  end
end
