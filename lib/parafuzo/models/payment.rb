# frozen_string_literal: true

require 'aasm'
require 'mongoid'
require 'mongoid_paranoia'

require_relative 'event'

class Payment
  include Mongoid::Document
  include Mongoid::Timestamps
  include Mongoid::Paranoia
  include AASM

  FINAL_STEPS       = %w(captured refunded cancelled denied).freeze
  PAID_STATES       = %w(captured accepted).freeze
  CAPTURABLE_STATES = %w(created accepted).freeze
  CHARGED_STATES    = (CAPTURABLE_STATES + PAID_STATES).freeze
  GATEWAYS = %i[stripe iugu transfeera woovi].freeze

  belongs_to :invoice
  belongs_to :order
  belongs_to :job
  belongs_to :user
  belongs_to :credit_card
  belongs_to :tasker

  has_many :events

  field :aasm_state
  field :type             , type: Symbol
  field :bill_url         , type: String
  field :bill_code        , type: String
  field :bill_date        , type: DateTime

  # In addition to being used in credit card operations, the `transaction_id` field is also used to store the `id` of
  #   the qrcode generated for pix operations.
  #
  field :transaction_id, type: String

  # Exclusive to pix operations.
  #
  field :pix_image_base64, type: String
  field :pix_code, type: String

  field :urgent, type: Boolean, default: false
  field :amount, type: Float, default: 0
  field :refunded_amount, type: Float, default: 0
  field :fee, type: Float, default: 0
  field :error_message    , type: String
  field :origin           , type: Symbol
  field :gateway          , type: Symbol
  field :paid_at          , type: DateTime, default: nil
  field :bank_name        , type: String
  field :created_at       , type: DateTime, default: -> { Time.current } # pending
  field :charged_at       , type: DateTime, default: nil                 # charge
  field :accepted_at      , type: DateTime, default: nil                 # checkout
  field :refunded_at      , type: DateTime, default: nil                 # refund
  field :failed_at        , type: DateTime, default: nil                 # fail
  field :denied_at        , type: DateTime, default: nil                 # deny
  field :captured_at      , type: DateTime, default: nil                 # capture
  field :cancelled_at     , type: DateTime, default: nil                 # cancel

  index({ deleted_at: 1, job_id: 1 }, { background: true })
  index({ deleted_at: 1, order_id: 1 }, { background: true })
  index({ invoice_id: 1 }, { background: true })
  index({ tasker_id: 1 }, { background: true })
  index({ transaction_id: 1 }, { background: true })
  index({ user_id: 1 }, { background: true })
  index({ aasm_state: 1, gateway: -1, created_at: -1 })

  validates_inclusion_of :gateway, in: GATEWAYS, allow_nil: true

  scope :capturable, -> { where(:aasm_state.in => CAPTURABLE_STATES) }
  scope :charged,    -> { where(:aasm_state.in => CHARGED_STATES) }
  scope :paid, -> { where(:aasm_state.in => PAID_STATES) }

  aasm do
    state :pending, initial: true
    state :created
    state :accepted
    state :captured
    state :refunded
    state :cancelled
    state :denied
    state :failed

    event(:charge)      { transitions from: :pending                        , to: :created  }
    event(:checkout)    { transitions from: :created                        , to: :accepted }
    event(:refund)      { transitions from: [:created, :accepted, :captured], to: :refunded }
    event(:fail)        { transitions                                         to: :failed }
    event(:deny)        { transitions from: [:pending, :created, :accepted] , to: :denied }
    event(:capture)     { transitions from: [:created, :accepted, :failed]  , to: :captured }
    event(:cancel)      { transitions from: [:pending, :created, :accepted] , to: :cancelled }

    before_all_events -> do
      timestamp_field = case aasm.current_event
                        when /charge!?/   then :charged_at=
                        when /checkout!?/ then :accepted_at=
                        when /refund!?/   then :refunded_at=
                        when /fail!?/     then :failed_at=
                        when /deny!?/     then :denied_at=
                        when /capture!?/  then :captured_at=
                        when /cancel!?/   then :cancelled_at=
                        end

      send(timestamp_field, Time.current)
    end
  end

  def paid?
    PAID_STATES.include? aasm_state.to_s
  end

  def expired?
    return false unless bill_date

    bill_date.to_date < Date.today
  end

  def has_final_step?
    FINAL_STEPS.include? aasm_state.to_s
  end

  def capturable?
    CAPTURABLE_STATES.include? aasm_state.to_s
  end

  def charged?
    CHARGED_STATES.include? aasm_state.to_s
  end
end
