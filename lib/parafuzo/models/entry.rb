# frozen_string_literal: true

require 'mongoid'
require 'mongoid_paranoia'

require_relative '../core/notify'
require_relative '../exception'
require_relative 'admin_user'
require_relative 'deal'
require_relative 'event'
require_relative 'invoice'
require_relative 'job'
require_relative 'tasker'
require_relative 'user'

# Model to represent a entry on tasker's statement.
#
class Entry
  # It's the start used to ignore entries on async tasker's statement
  # @see Payout::Statement::Packer::PROBLEMATIC_DATE_ON_STRIPE_TO_TRANSFEERA
  #
  PROBLEMATIC_INTERVAL_START_ON_STRIPE = Date.new(2021, 10, 24).beginning_of_day.freeze

  # It's the end used to ignore entries on async tasker's statement
  # @see Payout::Statement::Packer::PROBLEMATIC_DATE_ON_STRIPE_TO_TRANSFEERA
  #
  PROBLEMATIC_INTERVAL_END_ON_STRIPE = Date.new(2021, 11, 6).end_of_day.freeze

  include Mongoid::Document
  include Mongoid::Timestamps
  include Mongoid::Paranoia
  include Parafuzo::Core::Notify

  ENTRY_TYPES = %i[credit debit payment fee].freeze
  STATEMENT_TYPES = %i[credit debit payment].freeze
  ACTORS = %i[admin user system].freeze
  ACCOUNTING_TYPES = %i[credit debit].freeze
  GATEWAYS = %i[stripe iugu transfeera woovi].freeze

  belongs_to :tasker
  belongs_to :admin_user
  belongs_to :job
  belongs_to :user
  belongs_to :invoice
  belongs_to :deal

  has_many :events

  field :issued_at,      type: DateTime, default: -> { Time.current }
  field :refunded_at,    type: DateTime
  field :accumulated_at, type: DateTime

  field :number,                 type: String
  field :correlation_id,         type: String
  field :payment_correlation_id, type: String
  field :related_entries,        type: Array, default: []
  field :refunded_by,            type: String
  field :refunds,                type: String

  field :type,         type: Symbol
  field :actor,        type: Symbol
  field :value,        type: Float
  field :motive,       type: String
  field :gateway,      type: Symbol
  field :bonus_type,   type: Symbol

  field :payout_state, type: String
  field :payment_batch_name, type: String
  field :bank_receipt_url, type: String

  attr_accessor :services_count, :period_started_at

  default_scope -> { asc(:created_at) }

  scope :statement, ->(tasker) { where(tasker: tasker, :type.in => STATEMENT_TYPES).desc(:created_at).limit(45) }
  scope :payments,   ->(tasker) { unscoped.where(tasker: tasker, type: :payment) }
  scope :accounting, lambda { |tasker|
    accounting = where(tasker: tasker, :type.in => ACCOUNTING_TYPES)

    # @note query has a temporary solution to ignore problematic entries
    accounting = accounting.any_of({ :created_at.lt => PROBLEMATIC_INTERVAL_START_ON_STRIPE },
                                   { :created_at.gt => PROBLEMATIC_INTERVAL_END_ON_STRIPE })

    accounting
  }
  scope :not_marked_as_paid, -> { where(payment_correlation_id: nil) }

  index({ deal_id: 1 }, background: true)
  index({ deleted_at: 1, tasker_id: 1, type: 1 }, background: true)
  index({ gateway: 1 }, background: true)
  index({ job_id: 1 }, background: true)
  index({ number: 1 }, background: true)
  index({ payment_correlation_id: 1 }, background: true)
  index({ tasker_id: 1 }, background: true)
  index(type: 1, tasker: 1, created_at: -1)
  index({ tasker_id: 1, deleted_at: 1, payment_correlation_id: 1, type: 1, created_at: 1, gateway: 1, job_id: 1 },
        background: true)

  validates_uniqueness_of :number

  validates_presence_of :number
  validates_presence_of :type
  validates_presence_of :actor
  validates_presence_of :value

  validates_inclusion_of :actor, in: ACTORS
  validates_inclusion_of :type,  in: ENTRY_TYPES
  validates_inclusion_of :gateway, in: GATEWAYS, allow_nil: true

  validates_presence_of :tasker
  validates_presence_of :user, if: -> { actor == :user }
  validates_presence_of :motive, if: -> { (type == :debit || type == :credit) && actor == :admin }
  validates_presence_of :gateway, if: -> { type == :debit || type == :credit }

  before_destroy :prevent_altering

  def related
    return Entry.where(payment_correlation_id: number) if type == :payment

    Entry.where(correlation_id: number)
  end

  def denormalized_related
    Entry.find(related_entries)
  end

  def prevent_altering
    raise Parafuzo::Exception 'Entry cannot be deleted.'
  end

  def compensate!
    self.compensated_at = Time.current
  end

  def refunded?
    self.refunded_by != nil
  end

  def refund?
    self.refunds != nil
  end

  # return the entry that refunded this entry
  def refund_entry
    return unless self.refunded_by

    Entry.find(self.refunded_by)
  end

  # return the entry that this entry refunds
  def refunds_entry
    return unless self.refunds

    Entry.find(self.refunds)
  end

  def refundable?
    !(refunded_by || refunds || actor != :admin || payment_correlation_id.present? || type != :credit)
  end
end
