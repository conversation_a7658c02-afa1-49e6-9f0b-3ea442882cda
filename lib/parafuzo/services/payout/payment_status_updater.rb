# frozen_string_literal: true

require_relative '../../payout/statement/refunder'
require_relative '../../models/entry'

module Services
  module Payout
    # Updates the state of the {Entry}.
    #
    class PaymentStatusUpdater
      attr_reader :data

      def initialize(data)
        @data = data
      end

      def self.process(data)
        new(data).process
      end

      def process
        entry = Entry.find(data['entry_id'])

        return if entry.blank? || entry.payout_state != 'processing'

        case data['status']
        when 'success'
          do_success(entry, data)
        when 'fail'
          do_fail(entry, data)
        end
      end

      private

      def do_success(entry, data)
        gateway = data.fetch('gateway', entry.gateway)
        entry.update payout_state: 'accepted', bank_receipt_url: data['bank_receipt_url'], gateway:
      end

      # @note We don't use locales for refund message because it's a strugly feature to do work in functions.
      #
      def do_fail(entry, data)
        ::Payout::Statement::Refunder.new(entry).refund('Pagamento rejeitado.')
        gateway = data.fetch('gateway', entry.gateway)
        entry.update payout_state: 'rejected', gateway:
      end
    end
  end
end
