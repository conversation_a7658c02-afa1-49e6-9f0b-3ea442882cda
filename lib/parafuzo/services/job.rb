# frozen_string_literal: true

require_relative '../find_tasker/config'

class JobService
  include FindTasker::Config

  attr_reader :job, :changes, :slot_changes, :options, :notifier

  IGNORE_CHANGES = %w(payment_retries)
  MAXIMUM_TURBO_SCORE_COUNTER = 5

  def initialize(job, source = :system)
    @job, @changes = job, job.changes
    @slot_changes  = map_slot_changes
    @options       = { sms: true, source: source }
    @notifier      = Notifier.new(job, changes, slot_changes, options)
  end

  def run(options = {from_run: true})
    @options.merge! options

    refund_charged_payment if must_refund_to_recharge?

    unschedule_process if changes.include? 'date' and job.date.nil?
    change_job_preferential
    unassign           if job.assigned? and not job.with_all_taskers?
    assign             if job.pending?  and job.with_all_taskers? and not job.date.nil?
    check_changed_taskers
    run_processor
    run_bonus_service

    notifier.notify_user_tasker
  end

  def remove_tasker(tasker, **options)
    job_tasker = job.job_taskers.detect { |jt| jt.tasker_id == tasker.id }
    return false unless job_tasker

    job_tasker.tasker = nil
    job_tasker.clean_confirmations
    job.save!
    run(**options)
  end

  def unschedule
    unschedule_process
    job.date = nil

    @changes = job.changes
    run_processor

    job.save!
    create_event 'job.unscheduled'
  end

  def complete
    return true if job.completed?
    raise(Parafuzo::Exception, 'Job must have all taskers to be completed') unless job.with_all_taskers?

    Tasker.with_session do |session|
      session.start_transaction

      job.complete!

      create_event 'job.completed'

      invoice

      job.job_taskers.filter_map(&:tasker).each { |tasker| trigger_service_on(tasker) }

      turbo_score

      session.commit_transaction
    rescue StandardError => e
      session.abort_transaction

      raise e
    end

    Parafuzo::Core::Job.enqueue 'FeedbackRequest', job.id.to_s
    Parafuzo::Core::Job.enqueue 'Workers::TaskerFeedbackRequest', job.id.to_s
    Parafuzo::Core::Job.enqueue 'JobBuildNext', job.id.to_s
  end

  def uncomplete
    job.uncomplete!
    create_event 'job.uncompleted'

    refund_not_really_completed_invoices
  end

  def assign
    job.assign!
    create_event 'job.assign'
  end

  def force_assign(tasker_id, start_time)
    result = ForceAssign.new(tasker_id, job, start_time).run
    run if result[:success]
    result
  end

  def unassign
    job.unassign!
    create_event 'job.unassign'
  end

  def pending
    job.unassign!
    create_event 'job.pending'
  end

  def on_hold
    job.on_hold!
    create_event 'job.on_hold'
  end

  def cancel(options = {user_email: true})
    @options.merge! options

    job.cancel!

    slots.each do |slot|
      next if slot.tasker.nil? or not tasker_confirmed_preferential?(slot)

      notifier.notify_current_tasker :cancelled, slot
      create_event 'job_tasker.cancelled', slot.tasker
    end

    notifier.notify_user :job_cancelled if @options[:user_email]
    invalidate_offers

    create_event 'job.cancelled'

    refund_charged_payment
    refund_not_really_completed_invoices
    Parafuzo::Core::Job.enqueue 'JobBuildNext', job.id.to_s
  end

  def skip
    job.skip!

    slots.each do |slot|
      next if slot.tasker.nil? or not tasker_confirmed_preferential?(slot)

      notifier.notify_current_tasker :cancelled, slot
      create_event 'job_tasker.skipped', slot.tasker
    end

    notifier.notify_user :job_skipped
    invalidate_offers

    create_event 'job.skipped'

    refund_charged_payment
    refund_not_really_completed_invoices
  end

  def schedule
    return unless job.pending?
    return unless job.order.is_subscription?

    schedule_process
    create_event 'job.scheduled'
  end

  def pay
    if Payer.new(job).run
      create_event 'job.paid'
    end
  end

  def build_offer_for(tasker)
    begin
      job_tasker = job.job_taskers.find { |slot| slot.tasker_id.blank? }
      return nil if job_tasker.blank?

      offer_attributes = {
        available: true,
        bonus_type: job_tasker.bonus_type,
        payout_bonus: job_tasker.bonus_payout,
        job_id: job.id,
        job_tasker_id: job_tasker.id,
        order_id: job.order_id,
        job_date: job.date,
        replied_at: nil,
        state: 'pending',
        tasker_id: tasker.id,
        payout_in: :fixed,
        type: :default
      }
    rescue NameError => e
      Bugsnag.notify(e)
      return nil
    end

    Offer.new(offer_attributes)
  end

  def apply_credit
    ApplyCredit.new(job).run
  end

  private

  def refund_not_really_completed_invoices
    InvoiceRefunder.run job
  end

  def refund_charged_payment
    return unless job.payment
    return unless job.payment.charged?

    Payments::Refund.new(payment).refund!
  end

  def must_refund_to_recharge?
    return false unless job.payment
    return false unless job.payment.may_refund?

    # if job was rescheduled to a few days from now
    return true if time_diff_from_payment_to_job.hours > 72.hours

    job.price != job.payment.amount
  end

  def time_diff_from_payment_to_job
    ((job.date.to_time.to_f - job.payment.created_at.to_time.to_f).abs / 1.hour).round
  end

  def invoice
    service = InvoiceService.new job, :job
    service.create type: :full

    # TODO: Remove this because charge over invoice is deprecated
    service.enqueue_charge if !charged_payment? and job.price > 0

    job.invoice! if job.order.payment_type == :invoiced
  end

  def charged_payment?
    job.chargeable? or job.payments.any?(&:capturable?)
  end

  def debtor(motive = :debtor)
    ::OrderUserService.new(job.user).debtor(motive)
  end

  def invalidate_offers
    JobOffersService.new(job).invalidate
  end

  def build_next
    JobFactory.new(job.order).build_next
  end

  def schedule_process
    JobFactory.new(job.order).build_date(job)
  end

  def unschedule_process
    return if job.completed?

    job.unassign! unless job.pending?

    slots.each do |slot|
      next if slot.tasker.nil? or tasker_just_added?(slot) or not tasker_confirmed_preferential?(slot)

      notifier.notify_current_tasker :cancelled, slot
    end

    invalidate_offers

    slots.each do |slot|
      next unless slot.tasker

      slot.update_attribute :last_tasker_id, slot.tasker.id.to_s
      slot.update_attribute :tasker_id, nil
    end
  end

  def run_processor
    filter_keys(changes).each do |field, values|
      begin
        klass = "JobService::#{field.camelize}Changed"
        klass = Module.const_get klass
        klass.new(values, job, notifier, slot_changes).run
      rescue => e
      end
    end
    job.job_taskers.each do |slot|
      next if slot.blank?

      JobTaskerChanged.new(job, slot, slot_changes[slot.id.to_s]).run
    end
  end

  def check_changed_taskers
    return if job.date.nil?

    slots.each do |slot|
      process_changes(slot) if tasker_changed?(slot)
    end
  end

  def process_changes(slot)
    old_tasker_id, new_tasker_id = slot_changes[slot.id.to_s]['tasker_id']

    if old_tasker_id
      notifier.notify_previous_tasker :cancelled, slot
      slot.last_tasker_id = old_tasker_id
      slot.clean_confirmations
      slot.job.save
      create_event 'job_tasker.tasker_removed', old_tasker_id
    end

    return unless new_tasker_id

    notifier.notify_schedule(slot)
    create_event 'job_tasker.tasker_assigned', new_tasker_id
  end

  def change_job_preferential
    FindTasker::Preferential.new(job).change
  end

  def run_bonus_service
    BonusService.new(job).run
  end

  def tasker_just_added?(slot)
    tasker_changed?(slot) and slot_changes_for(slot.id.to_s)['tasker_id'][0].nil?
  end

  def tasker_confirmed_preferential?(slot)
    return true unless order_tasker(slot.tasker)

    order_tasker(slot.tasker).confirmed?
  end

  def order_tasker(tasker)
    job.order.order_taskers.detect { |ot| ot.tasker == tasker }
  end

  def tasker_changed?(slot)
    slot_changes_for(slot.id.to_s).has_key? 'tasker_id'
  end

  def slot_changes_for(slot_id)
    slot_changes[slot_id] || {}
  end

  def map_slot_changes
    Hash[slots.map{|s| [s.id.to_s, s.changes] }]
  end

  def filter_keys(hash)
    hash.delete_if { |k, _| IGNORE_CHANGES.include? k }
  end

  def slots
    job.job_taskers
  end

  def all_changes_keys
    changes.keys + slot_changes.map { |_id, slot_change| slot_change.keys }.flatten
  end

  def capture_date
    Date.today.beginning_of_day.change(hour: 21)
  end

  def trigger_service_on(tasker)
    tasker_service = TaskerService.new(tasker)
    tasker_service.charge_subscription_fee

    tasker_service.enable_tasker? ? tasker_service.activate : tasker.save!
  end

  def preferential_tasker?(tasker)
    job.preferential && order_tasker(tasker)&.confirmed?
  end

  def create_event(name, tasker = nil)
    event_service.create_event(
      name,
      job: job,
      user: job.user,
      tasker: tasker,
      source: "job_service.#{options[:source]}",
      data: { options: options, changes: changes, slot_changes: slot_changes },
      track_changes: [job, *job.job_taskers]
    )
  end

  def event_service
    @event_service ||= EventService.new
  end

  def turbo_score
    taskers = slots.map(&:tasker)
    service = job.service

    taskers.each do |tasker|
      counter = tasker.turbo_score_counter_for(service) + 1
      tasker.scores[service] ||= {}
      tasker.scores[service][:turbo_score_counter] = counter
      if counter >= MAXIMUM_TURBO_SCORE_COUNTER
        feedback = tasker.feedbacks.build
        FeedbackService.new(feedback).new_review(
          review: I18n.t(:turbo_compliments).sample.gsub(/\\#/, '#'),
          tags: %w[avaliação turbinada],
          score: 5,
          service: service
        )
        tasker.scores[service][:turbo_score_counter] = 0
      end
      tasker.save
    end
  end

  autoload :ActionVerifier, 'services/job/action_verifier'
  autoload :ApplyCredit, 'services/job/apply_credit'
  autoload :DateChanged         , 'services/job/date_changed'
  autoload :Notifier            , 'services/job/notifier'
  autoload :Payer               , 'services/job/payer'
  autoload :ForceAssign         , 'services/job/force_assign'
  autoload :JobTaskerChanged    , 'services/job/job_tasker_changed'
end
