# frozen_string_literal: true

require_relative '../../models/job'
require_relative '../../models/tasker'
require_relative '../../models/tasker_history_job'
require_relative 'history_job/changes_checker'
require_relative 'history'

module Taskers
  # Handles inserts and deletes of model {TaskerHistoryJob}.
  #
  class HistoryJob
    attr_reader :job_before, :job_after

    delegate :changed_to_completed_states?, :changed_from_completed_states?, :date_changed_with_completed_states?,
             :included_tasker_on_completed_job?, :removed_tasker_from_completed_job?, to: :changes_checker

    def initialize(job_before: nil, job_after: nil)
      @job_before = job_before
      @job_after = job_after
    end

    def compute
      return if without_jobs?

      TaskerHistoryJob.with_session do |session|
        session.start_transaction

        fidelize_or_unfidelize
        create_or_delete_history_jobs

        session.commit_transaction
      rescue StandardError => e
        session.abort_transaction

        raise e
      end
    end

    private

    def history_service
      @history_service ||= Taskers::History.new
    end

    def changes_checker
      @changes_checker ||= Taskers::HistoryJob::ChangesChecker.new job_before: job_before, job_after: job_after
    end

    def without_jobs? = job_before.nil? || job_after.nil?

    def fidelize_or_unfidelize
      unfidelize_history_jobs if changed_from_completed_states? || date_changed_with_completed_states?

      fidelize_history_jobs if changed_to_completed_states? || date_changed_with_completed_states?

      update_history_jobs if date_changed_with_completed_states?
    end

    def create_or_delete_history_jobs
      job_before['job_taskers']&.each { |job_tasker| delete_history_job(job_tasker['tasker_id']) }

      job_after['job_taskers']&.each { |job_tasker| create_history_job(job_tasker['tasker_id']) }
    end

    def create_history_job(tasker_id)
      return if tasker_id.nil?
      return unless create_history_job?(tasker_id)

      history_service.complete_job(tasker: Tasker.find(tasker_id)).save!
      preferential = job_after['job_taskers'].find { _1['tasker_id'] == tasker_id }['performed_by_preferential']

      TaskerHistoryJob.create job_id: job_after['_id'],
                              tasker_id: tasker_id,
                              job_date: job_after['date'],
                              performed_by_preferential: preferential,
                              subscription: job_after['subscription'],
                              user_id: job_after['user_id']
    end

    def delete_history_job(tasker_id)
      return if tasker_id.nil?
      return unless delete_history_job?(tasker_id)

      history_job = TaskerHistoryJob.find_by job_id: job_before['_id'], tasker_id: tasker_id
      return if history_job.blank?

      history_service.fidelize_job(tasker: history_job.tasker, operation: :decrease) if history_job.fidelized?
      history_service.complete_job(tasker: history_job.tasker, operation: :decrease).save!

      history_job.destroy!
    end

    def update_history_jobs
      fidelizing_date = user_next_jobs.first&.date
      fidelizing_jobs = fidelizing_date.blank? ? [] : user_next_jobs.where(date: fidelizing_date.all_day)

      TaskerHistoryJob.where(job_id: job_after['_id']).each do |history_job|
        history_job.update! job_date: job_after['date'],
                            fidelized_by_id: fidelizing_jobs.first&.id,
                            fidelized_at: fidelizing_date,
                            fidelized_by_job_ids: fidelizing_jobs.map(&:id)

        history_service.fidelize_job(tasker: history_job.tasker).save! if fidelizing_jobs.any?
      end
    end

    def create_history_job?(tasker_id)
      changed_to_completed_states? || included_tasker_on_completed_job?(tasker_id)
    end

    def delete_history_job?(tasker_id)
      changed_from_completed_states? || removed_tasker_from_completed_job?(tasker_id)
    end

    def fidelize_history_jobs
      fidelized_date = user_last_jobs.last&.date
      fidelized_jobs = fidelized_date.blank? ? [] : user_last_jobs.where(date: fidelized_date.all_day)
      return if fidelized_jobs.blank?

      TaskerHistoryJob.where(:job.in => fidelized_jobs.to_a).each do |history_job|
        fidelized_by_job_ids = history_job.fidelized_by_job_ids
        fidelized_by_job_ids << BSON::ObjectId(job_after['_id'])

        history_job.update!(fidelized_by_id: job_after['_id'], fidelized_at: job_date, fidelized_by_job_ids:)

        history_service.fidelize_job(tasker: history_job.tasker).save! if fidelized_by_job_ids.one?
      end
    end

    def user_last_jobs
      Job.completed.where(
        user_id: job_after['user_id'],
        date: (job_date - 90.days).beginning_of_day..(job_date - 1.day).end_of_day,
        :id.ne => job_after['_id']
      ).order(:date.asc, :completed_at.asc)
    end

    def user_next_jobs
      Job.completed.where(
        user_id: job_after['user_id'],
        date: (job_date + 1.day).beginning_of_day..(job_date + 90.days).end_of_day,
        :id.ne => job_after['_id']
      ).order(:date.asc, :completed_at.asc)
    end

    def unfidelize_history_jobs
      fidelized_history_jobs = TaskerHistoryJob.where fidelized_by_job_ids: job_before['_id']

      fidelized_history_jobs.each do |history_job|
        fidelized_by_job_ids = history_job.fidelized_by_job_ids
        fidelized_by_job_ids.delete BSON::ObjectId(job_after['_id'])

        history_job.update!(fidelized_by_id: nil, fidelized_at: nil, fidelized_by_job_ids:)
        next if history_job.fidelized?

        history_service.fidelize_job(tasker: history_job.tasker, operation: :decrease).save!
      end
    end

    def job_date
      @job_date ||= Time.parse(job_after['date']).in_time_zone
    end
  end
end
