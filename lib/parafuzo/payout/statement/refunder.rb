# frozen_string_literal: true

require_relative '../../services/deal'
require_relative 'base'
require_relative 'creator'

class Payout::Statement::Refunder < Payout::Statement::Base
  attr_reader :entry

  REFUNDABLE = %i[credit debit payment].freeze
  REVERSES   = { credit: :debit, debit: :credit, payment: :credit }.freeze

  def self.refund_invoice(invoice, motive = nil, admin = nil)
    entries = find_by_invoice(invoice)

    entries.each do |entry|
      new(entry).refund(motive, admin)
    end

    invoice.deals.each do |deal|
      DealService.new(tasker: invoice.tasker, deal: deal).cancel_payment(invoice)
    end
  end

  def initialize(entry)
    @entry = entry
  end

  def refund(motive = nil, admin = nil)
    return unless can_refund?(entry)

    refund = create_refund_entry(entry, motive, admin)

    set_refunded(entry, refund)
  end

  private

  def self.find_by_invoice(invoice)
    Entry.where(invoice: invoice, :type.in => %i[credit debit], refunded_at: nil)
  end

  def can_refund?(entry)
    REFUNDABLE.include?(entry.type) && entry.refunded_at.nil?
  end

  def create_refund_entry(entry, motive, admin) # rubocop:disable Metrics/MethodLength
    params = {
      job:        entry.job,
      user:       entry.user,
      invoice:    entry.invoice,
      admin_user: admin,
      value:      entry.value,
      type:       reversed_type(entry.type),
      refunds:    entry.id.to_s,
      motive:     job_motive(motive) || motive,
      gateway:    entry.gateway || :iugu
    }
    Payout::Statement::Creator.new(entry.tasker, params, actor: admin ? :admin : :system).create!
  end

  def reversed_type(type)
    REVERSES[type]
  end

  def set_refunded(entry, refund)
    entry.update_attributes(
      refunded_at: Time.current,
      refunded_by: refund.id.to_s
    )
  end

  def job_motive(motive)
    return unless entry.job

    "#{motive} diária #{entry.job.number}"
  end
end
