# frozen_string_literal: true

namespace :payouts do
  namespace :batch do
    desc "Create batch of taskers' payout"
    task :create do # rubocop:disable Rails/RakeEnvironment
      Parafuzo::Core.logger.info "Starting batch of taskers' payout"

      entries = Entry.includes(:tasker).where(
        type: :payment,
        payout_state: 'processing',
        gateway: :transfeera,
        payment_batch_name: nil,
        :created_at.gte => 2.weeks.ago
      )

      transfers = entries.batch_size(100).map do |entry|
        tasker = entry.tasker
        bank = tasker.bank

        { entry_id: entry.id.to_s, tasker_name: tasker.name, tasker_cpf: tasker.cpf, tasker_email: tasker.email,
          bank_code: bank.code, bank_agency: bank.agency, bank_account: bank.account, bank_account_type: bank.type,
          value: (entry.value.round(2) * 100).round }
      end

      Parafuzo::Core::Queue.publish('payments-do-pay', { transfers: }) if transfers.present?

      Parafuzo::Core.logger.info "Finished batch of taskers' payout"
    end
  end
end
