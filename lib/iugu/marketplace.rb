# frozen_string_literal: true

$LOAD_PATH.unshift File.expand_path('../../lib', __dir__)

require 'iugu/marketplace/version'
require 'i18n'
require 'json'
require 'faraday'
require 'active_support/core_ext/hash/keys'

I18n.load_path += Dir[File.expand_path('../../config/locales/**/*.yml', __dir__)]

module Iugu
  # Iugu Marketplace API integration.
  #
  module Marketplace
    def self.config
      Config.instance
    end

    def self.configure
      yield(config)
    end

    autoload :Config, 'iugu/marketplace/config'
    autoload :Object, 'iugu/marketplace/object'
    autoload :Request, 'iugu/marketplace/request'
    autoload :Error, 'iugu/marketplace/error'
    autoload :Token, 'iugu/marketplace/token'
    autoload :Charge, 'iugu/marketplace/charge'
    autoload :Invoice, 'iugu/marketplace/invoice'
    autoload :Customer, 'iugu/marketplace/customer'

    module Methods
      autoload :Base, 'iugu/marketplace/methods/base'
      autoload :Create, 'iugu/marketplace/methods/create'
      autoload :Capture, 'iugu/marketplace/methods/capture'
      autoload :Cancel, 'iugu/marketplace/methods/cancel'
      autoload :Refund, 'iugu/marketplace/methods/refund'
      autoload :Update, 'iugu/marketplace/methods/update'
      autoload :Delete, 'iugu/marketplace/methods/delete'
      autoload :Find, 'iugu/marketplace/methods/find'
      autoload :All, 'iugu/marketplace/methods/all'
    end
  end
end
