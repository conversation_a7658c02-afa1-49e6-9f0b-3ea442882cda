# frozen_string_literal: true

require 'functions_framework'

FunctionsFramework.on_startup do
  require 'base64'
  require 'json'

  require_relative 'config/initializers/time_zone'
  require_relative 'config/initializers/mongoid'
  require_relative 'config/initializers/redis'
  require_relative 'config/initializers/resque_client'
  require_relative 'config/initializers/api_url'
  require_relative 'lib/parafuzo/services/payout/batch_name_updater'
  require_relative 'lib/parafuzo/services/payout/payment_status_updater'
end

FunctionsFramework.cloud_event 'poseidon-payout-batch_name_updater' do |event|
  Services::Payout::BatchNameUpdater.process(JSON.parse(Base64.decode64(event.data['message']['data'])))

  # puts '-' * 30
  # puts $LOADED_FEATURES.select { |feature| feature.starts_with? '/app/' }.sort
  # puts '-' * 30

  true
end

FunctionsFramework.cloud_event 'poseidon-payout-payment_status_updater' do |event|
  Services::Payout::PaymentStatusUpdater.process(JSON.parse(Base64.decode64(event.data['message']['data'])))

  true
end
