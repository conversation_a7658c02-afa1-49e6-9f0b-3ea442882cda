# frozen_string_literal: true

Rails.application.routes.draw do
  root 'dashboard#index'

  resources :messages, only: [:index]
  resources :admin_users
  resources :stored_config, param: :name, only: [:edit, :update]
  resources :blocked_addresses
  resources :feedbacks
  resources :incidents
  resources :companies
  resources :events
  resources :services, only: [:index, :edit, :update]
  resources :tasker_feedbacks
  resources :address_references, except: :destroy

  resources :hot_sites do
    post  'duplicate' , on: :member
    patch 'publish'   , on: :member
    patch 'unpublish' , on: :member
    get   'block'     , on: :collection
  end

  resources :coupons do
    patch 'expire'   , on: :member
    get   'validate' , on: :member
  end

  resources :taskers do
    get  "statement",  to: "entries#statement"
    post "pay",        to: "entries#pay"
    put  "activate",   to: "taskers#activate", on: :member
    put  "deactivate", to: "taskers#deactivate", on: :member
    get  "validate",   to: "taskers#validate", on: :member
    put 'background_check', to: 'taskers#background_check', on: :member
    put 'mei_check', to: 'taskers#mei_check', on: :member
    patch 'reset_password', to: 'taskers#reset_password', on: :member
    resources :admin_notes, only: %i[index new create destroy] do
      resource :pins, only: %i[create destroy], controller: 'admin_notes/pins'
    end
    resources :devices do
      patch "logout", on: :member
    end
    namespace :taskers do
      resource :scores, only: [:edit]
    end

    resources :history_jobs, only: :index, controller: 'taskers/tasker_history_jobs'
    resources :opportunities, only: :index, controller: 'taskers/opportunities'

    resources :processed_feedbacks, only: %i[index], controller: 'taskers/processed_feedbacks' do
      post :disable, on: :member
      post :enable, on: :member
    end
  end

  resources :deals, only: [:index, :new, :create, :show] do
    put :cancel, on: :member
  end

  resources :payments do
    patch "refund", on: :member
    get 'receipt', on: :member
  end

  resources :users do
    get   'customers'          , on: :collection
    get   'offices'            , on: :collection
    get 'balance', on: :member
    patch 'debtor'             , on: :member
    patch 'activate'           , on: :member
    resources :admin_notes, only: %i[index new create destroy] do
      resource :pins, only: %i[create destroy], controller: 'admin_notes/pins'
    end
    resources :credit_cards, only: %i[update new create destroy] do
      patch 'verify', on: :collection
    end
    resources :blocked_taskers, only: %i[index new create destroy], controller: 'users/blocked_taskers'
  end

  resources :orders, except: :new do
    patch 'checkout'   , to: 'orders#checkout' , on: :collection
    post  'checkout'   , to: 'orders#checkout' , on: :collection
    get   'price'      , to: 'orders#price'    , on: :collection
    patch 'cancel'     , to: 'orders#cancel'
    patch 'pay'        , to: 'orders#pay'
    patch 'new_bill'   , to: 'orders#new_bill'
    patch 'finish'     , to: 'orders#finish', on: :member
    patch 'geocode'    , to: 'orders#geocode', on: :member
    patch 'pause'      , on: :member
    patch 'resume'     , on: :member
    patch 'invoice'    , on: :member
    patch 'build_next' , on: :member
    resources :admin_notes, only: %i[index new create destroy] do
      resource :pins, only: %i[create destroy], controller: 'admin_notes/pins'
    end
    resources :order_taskers, only: :confirm do
      put 'confirm', on: :member
    end

    resources :build_extra_jobs, only: %i[new create], controller: 'orders/build_extra_job'

    resource :address, controller: 'orders/address', only: %i(edit update)
  end

  get 'orders/new/:service', to: "orders#new", as: 'new_order'

  resources :jobs do
    patch 'skip'            , on: :member
    patch 'reschedule'      , on: :member
    patch 'change'          , on: :member
    patch 'complete'        , on: :member
    patch 'uncomplete'      , on: :member
    patch 'pay'             , on: :member
    get   'express'         , on: :collection
    get   'upcoming'        , to: 'jobs#upcoming'       , on: :collection
    get   'unconfirmed'     , to: 'jobs#unconfirmed'    , on: :collection
    get   'pending_payment' , to: 'jobs#pending_payment', on: :collection
    patch 'remove_tasker'   , to: 'jobs#remove_tasker'  , on: :collection
    patch 'cancel'          , to: 'jobs#cancel'
    get   'taskers_near'    , on: :member
    patch 'payment_confirm' , on: :member
    patch 'force_charge'    , on: :member
    resources :admin_notes, only: %i[index new create destroy] do
      resource :pins, only: %i[create destroy], controller: 'admin_notes/pins'
    end

    resource :incidents do
      patch "cancel"    , to: "incidents#cancel" , on: :collection
      patch "delay"     , to: "incidents#delay"  , on: :collection
      patch "no_show"   , to: "incidents#no_show", on: :collection
    end

    resources :chats, only: %i[index], controller: 'jobs/chats' do
      patch 'freeze', to: 'jobs/chats#freeze', on: :member
    end

    resources :job_taskers, only: [] do
      patch 'clean_check_in_holdback', to: 'job_taskers#clean_check_in_holdback', as: :clean_check_in_holdback
    end
  end

  resources :invoices do
    patch 'cancel'        , on: :member
    patch 'pay'           , on: :member
    patch 'unpay'         , on: :member
    patch 'generate_bill' , on: :member
    patch 'charge'        , on: :member

    resources :payments
  end

  resources :offers do
    put 'accept', on: :member
    put 'reject', on: :member
    put 'assign', on: :member
  end

  resources :entries do
    delete "refund", to: "entries#refund"
  end

  get  'photos/:photographic_id/:id'      , to: 'photos#show', as: "photo"
  post 'photos/:photographic_id/:id/crop' , to: 'photos#crop', as: "photo_crop"

  get 'cep-search/:cep' , to: 'cep_search#search'
  get 'dashboard'       , to: "dashboard#index"

  devise_for(
    :admin_users,
    path: 'auth',
    skip: :registrations,
    path_names: {
      sign_in: 'login', sign_out: 'logout', password: 'secret', confirmation: 'verification',
      unlock: 'unblock', registration: 'register', sign_up: 'signin'
    }
  )
  as :admin_user do
    get 'profile/edit' => 'devise/registrations#edit', as: 'edit_admin_user_registration'
    put 'admin_users' => 'devise/registrations#update', as: 'admin_user_registration'
  end

  mount JasmineRails::Engine => '/specs' if defined?(JasmineRails)
  mount JasmineFixtureServer => '/spec/javascripts/fixtures' if defined?(Jasmine::Jquery::Rails::Engine)
end
