# frozen_string_literal: true

require 'rails_helper'

describe AdminNotes::PinsController do
  describe '#create' do
    let(:admin) { create :admin_user, :admin, email: '<EMAIL>' }
    let!(:admin_note) { create :admin_note }
    let(:tasker) { create :tasker }

    before { sign_in admin }

    def do_post
      post :create, params: { admin_note_id: admin_note.id.to_s, tasker_id: tasker.id.to_s }
    end

    it 'POST /' do
      expect { do_post }.to(change { admin_note.reload.pinned_at })
    end
  end

  describe '#destroy' do
    let(:admin) { create :admin_user, :admin, email: '<EMAIL>' }
    let!(:admin_note) { create :admin_note, pinned_at: Time.zone.now }
    let(:tasker) { create :tasker }

    before { sign_in admin }

    def do_delete
      delete :destroy, params: { admin_note_id: admin_note.id.to_s, tasker_id: tasker.id.to_s }
    end

    it 'DELETE /' do
      expect { do_delete }.to(change { admin_note.reload.pinned_at })
    end

    describe 'when admin note is not pinned' do
      before { admin_note.update(pinned_at: nil) }

      it 'expect to not change pinned_at' do
        expect { do_delete }.not_to(change { admin_note.reload.pinned_at })
      end
    end
  end
end
