# frozen_string_literal: true

require 'rails_helper'

describe IncidentsController do
  let(:user)        { create :admin_user, :admin }
  let(:order)       { create :order, :cleaning }
  let(:tasker)      { create :tasker, name: '<PERSON>', phones: [build(:phone, number: '11911111111', main: true)], coordinates: [-20, -40] }
  let(:job_tasker)  { build  :job_tasker, tasker: tasker }
  let(:job)         { create :job, order: order, date: 1.day.from_now, job_taskers: [job_tasker] }
  let(:job_service) { double run: true }

  before do
    sign_in user
  end

  it { expect(described_class.ancestors).to include(Indexable) }

  describe "GET /new" do
    def do_get
      get :new, params: { job_id: job.id, tasker_id: tasker.id, type: 'delayed' }
    end

    it "should be success" do
      do_get
      expect(response).to be_successful
    end

    it "should initialize model with correct params" do
      do_get
      expect(assigns(:incident).type).to eq :delayed
    end
  end

  describe "POST /" do
    let(:params) { {
      contacted_by:  'user',
      job_id:        job.id,
      job_tasker_id: job_tasker.id,
      tasker_id:     tasker.id,
      type:          'delayed',
      metadata:      { notes: "notes" }
    } }

    def do_post
      post :create, params: { incident: params }
    end

    it "should create incident" do
      expect_any_instance_of(IncidentService).to receive(:delayed)
      do_post
    end

    it "should redirect to job page" do
      do_post
      expect(response).to redirect_to(job_path(assigns(:job)))
    end

    context "no show should run service" do
      let(:params) {{
        contacted_by:  'user',
        job_id:        job.id,
        job_tasker_id: job_tasker.id,
        tasker_id:     tasker.id,
        type:          'no_show',
        metadata:      { notes: "notes" }
      }}
      let(:service) { double no_show: true }

      before do
        allow(IncidentService).to receive(:new).with(job, job_tasker, tasker, params.merge({ admin_user: user })).and_return service
      end

      it "should run service" do
        expect(service).to receive(:no_show)
        do_post
      end
    end
  end

  describe "GET /" do
    render_views
    before do
      @incidents = 2.times.map { create :incident, :no_show, job: job }
    end

    it "should be success" do
      get :index, params: { job_id: job.id }
      expect(response).to be_successful
    end

    it "should se records" do
      get :index, params: { job_id: job.id }
      expect(assigns(:records).to_a.size).to eq(2)
    end

    it 'dont list deleted' do
      get :index, params: { job_id: job.id }, format: :json
      expect(assigns(:records).to_a.size).to eq(2)

      @incidents.last.destroy(create(:admin_user))

      get :index, params: { job_id: job.id }
      expect(assigns(:records).to_a.size).to eq(1)
    end
  end

  describe 'DELETE /:id' do
    def do_delete
      request.env["HTTP_REFERER"] = "refer"
      delete :destroy, params: { id: incident.id }
    end

    let(:incident) { create(:incident, :cancel) }

    it 'mark incident as deleted' do
      do_delete
      expect(Incident.find(incident.id)).to eq(nil)
      expect(Incident.deleted.find(incident.id).id.to_s).to eq(incident.id.to_s)
    end

    it 'flash success' do
      do_delete
      expect(flash[:success]).to eq("Incidente removido.")
    end

    context 'incident used by feedback' do
      let(:feedback) { create(:feedback, :incident) }
      let(:incident) { feedback.incident }

      context 'cannot delete incident with feedback' do
        it 'dont soft delete incident' do
          do_delete
          i = Incident.find(incident.id)
          expect(i.id).to eq(incident.id)
          expect(i.deleted_at).to eq(nil)
          expect(i.deleted_by).to eq(nil)
        end

        it 'flash error' do
          do_delete
          expect(flash[:error]).to eq("Incidente não pode ser removido pois existe feedback(#{feedback.id}) associado.")
        end
      end
    end

    context 'non admin user cannot remove incidents' do
      let(:user) { create :admin_user }

      it 'dont remove' do
        do_delete
        i = Incident.find(incident.id)
        expect(i.id).to eq(incident.id)
      end

      it 'flash error' do
        do_delete
        expect(flash[:error]).to eq("ação não autorizada")
      end
    end
  end

  describe "PATCH cancel" do
    def do_patch
      patch :cancel, params: { job_id: job.id, tasker_id: tasker.id }
    end

    it "should call incident service" do
      expect_any_instance_of(IncidentService).to receive(:cancel)
      do_patch
    end

    it "should redirect to show" do
      expect(do_patch).to redirect_to(job_path(assigns(:job)))
    end

    it "should create a cancel incident" do
      do_patch
      incident = Incident.last
      expect(incident.type).to eq :cancel
      expect(incident.admin_user).to eq user
    end
  end
end
