# frozen_string_literal: true

class IncidentDecorator < Draper::Decorator
  TABLE_LIST_COLUMNS = {
    incident_at: { width: 2 },
    job_number: { width: 1, sorting: false },
    tasker_name: { width: 3, sorting: false },
    time_delta: { width: 3, sorting: false },
    type: { width: 1 },
    severity: { width: 1 },
    action: { width: 1, sorting: false }
  }.freeze

  COLUMNS = [:incident_at, 'job.date', 'tasker.name', :time_delta, :type].freeze

  delegate_all
end
