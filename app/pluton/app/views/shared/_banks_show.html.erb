<div class="well">
  <div class="table-responsive">
    <table class="table" id="bank-accounts">
      <thead>
        <tr>
          <th>Banco</th>
          <th>Agência</th>
          <th>Conta</th>
          <th>Tipo</th>
          <th>Principal?</th>
          <th>Validação</th>
          <th>Erros</th>
          <th>Atualizada em</th>
        </tr>
      </thead>
      <tbody>
        <% for bank in banks %>
          <tr>
            <td><%= bank.code_humanize %></td>
            <td><%= bank.agency %></td>
            <td><%= bank.account %></td>
            <td><%= t(bank.type, scope: [:bank]) unless bank.type.blank? %></td>
            <td><%= t(bank.main.to_s) unless bank.main.nil? %></td>
            <td>
              <span class='label label-<%= external_account_label(bank.check) %>'>
                <%= t(bank.check, scope: [:bank]) %>
              </span>
            </td>
            <td>
              <span class="text-copy" data-toggle="popover" data-content="<%= bank.check_errors %>">
                <%= bank.check_errors&.truncate(20) %>
              </span>
            </td>
            <td><%= l bank.updated_at %></td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
