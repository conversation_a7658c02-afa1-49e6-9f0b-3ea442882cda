<div class="dataTables_wrapper no-footer">
  <div class="row datatables-header form-inline">
    <%= form_tag(current_path, method: 'get') do %>
      <% params[:filter]&.permit(permitted_filter_params)&.each do |name, options| %>
        <% options.each do |attribute, value| %>
          <%= hidden_field_tag "filter[#{name}][#{attribute}]", value %>
        <% end %>
      <% end %>

      <% if local_assigns.fetch(:choose_list_length, true) %>
        <div class="col-sm-12 col-md-6">
          <div class="dataTables_length">
            <label>
              <%= select_tag(:length, options_for_select([10, 25, 50, 100], params[:length]), data: { controller: 'auto-submit', action: 'change->auto-submit#submit' }, class: 'form-control') %>
              resultados por página
            </label>
          </div>
        </div>
      <% end %>

      <% if local_assigns.fetch(:search_bar, true) %>
        <div class="col-sm-12 col-md-6">
          <div class="dataTables_filter">
            <label>
              <%= text_field_tag 'search[value]', params.dig(:search, :value), class: 'form-control' %>
            </label>
          </div>

          <%= yield if block_given? %>
        </div>
      <% end %>
    <% end %>
  </div>

  <div class="table-responsive">
    <table class="table table-bordered table-striped mb-none" width="100%">
      <thead>
        <tr>
          <% decorator::TABLE_LIST_COLUMNS.each_with_index do |(column, props), position| %>
            <%= content_tag :th, class: "#{props.fetch(:sorting, true) && position == sorting_param ? "sorting_#{sorting_direction}" : "sorting"} col-xs-#{props[:width]}" do %>
              <% if props.fetch(:sorting, true) %>
                <%=
                  link_to t(column, scope: [controller_name, action_name]),
                          current_path(order: { '0' => { column: position, dir: sorting_direction == :desc ? :asc : :desc } }),
                          style: 'color: #777',
                          data: { turbo: true }
                %>
              <% else %>
                <%= t(column, scope: [controller_name, action_name]) %>
              <% end %>
            <% end %>
          <% end %>
        </tr>
      </thead>
      <tbody>
        <%= render(partial: 'table_list_row', collection: records, as: controller_name.singularize) ||
              render('shared/empty_table_row', colspan: decorator::TABLE_LIST_COLUMNS.size) %>
      </tbody>
    </table>
  </div>

  <% if local_assigns.fetch(:paginate, true) %>
    <div class="row datatables-footer">
      <div class="col-sm-12 col-md-6 dataTables_info">
        <%= page_entries_info records %>
      </div>
      <div class="col-sm-12 col-md-6">
        <%= paginate records %>
      </div>
    </div>
  <% end %>
</div>
