<tr data-turbo="false">
  <td><%= link_to l(incident.incident_at), incident %></td>
  <td><%= link_to incident.job.number, incident.job %></td>
  <td><%= link_to incident.tasker.name, incident.tasker %></td>
  <td><%= time_delta_human(incident.time_delta) %></td>
  <td>
    <% type_labels = { no_show: 'danger', delayed: 'warning', cancel: 'info' } %>

    <%= content_tag(:span, class: "label label-#{type_labels[incident.type].presence || 'default'}") do %>
      <%= Incident.human_attribute_name("type/#{incident.type}") %>
    <% end %>
  </td>
  <td>
    <%= content_tag(:span, class: "label label-#{incident.severity == :normal ? 'warning' : 'danger'}") do %>
      <%= Incident.human_attribute_name("severity/#{incident.severity}") %>
    <% end %>
  </td>
  <td>
    <% if can?(:destroy, incident) %>
      <%=
        link_to_if can?(:destroy, incident), 'Remover', incident, method: :delete,
                                     data: { confirm: 'Tem certeza que deseja remover este incidente?' },
                                     class: 'btn btn-danger btn-xs'
      %>
    <% end %>
  </td>
</tr>
