/*global Module, PLUTON, _ */

Module("PLUTON.modules.adminNotesDatatable", function (module) {
  module.fn.initialize = function (table, scope) {
    this.table = $(table);
    this.requestURL = this.table.data("url") || "";
    this.table.dataTable(this._tableOptions());
  };

  function renderActionsButtonDataTable(data, type, full, meta) {
    const [_, parentClass, parentId] = meta.settings.ajax.split("/");

    const basePath = `/${parentClass}/${parentId}/admin_notes/${full.id}`;
    const pinActions = {
      pin: {
        method: 'post',
        label: 'Fixar',
      },
      unpin: {
        method: 'delete',
        label: 'Desfixar',
      },
    };

    const actionKey = full.pinned_at ? 'unpin' : 'pin';

    const pinBtn = `<a 
      href="${basePath}/pins/"
      data-method="${pinActions[actionKey].method}"
      class="btn btn-warning btn-xs">
      ${pinActions[actionKey].label}
    </a>`;

    const removeBtn = `<a href="/${parentClass}/${parentId}/admin_notes/${full.id}"
      data-method="delete"
      data-confirm="Tem certeza que deseja remover este comentário?"
      class="btn btn-danger btn-xs">Remover</a>`;
    return `${pinBtn} ${removeBtn}`;
  }

  module.fn._tableOptions = function () {
    return {
      language: {
        lengthMenu: "_MENU_ resultados por página",
        zeroRecords: "Nenhum resultado encontrado",
        info: "Exibindo página _PAGE_ de _PAGES_",
        infoEmpty: "Nenhum resultado disponível",
        infoFiltered: "(filtered from _MAX_ total records)",
      },
      serverSide: true,
      ajax: this.requestURL,
      order: [[2, "desc"]],
      columns: [
        { data: "admin_user" },
        { data: "note" },
        { data: "created_at" },
        {
          data: null,
          render: renderActionsButtonDataTable,
        },
      ],
    };
  };
});
