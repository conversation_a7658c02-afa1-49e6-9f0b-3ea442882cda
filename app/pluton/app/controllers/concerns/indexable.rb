# frozen_string_literal: true

module Indexable
  extend ActiveSupport::Concern

  # Optional fields used to filter results in some requests.
  #
  SEARCH_FIELDS = %i[job_id order_id user_id tasker_id admin_user_id invoice_id transfer_id pack_id company_id].freeze

  # Actions to always show search results even without filters.
  #
  ACTIONS_TO_SHOW_SEARCH_RESULT = [
    'companies#index',
    'jobs#express',
    'jobs#unconfirmed',
    'jobs#upcoming',
    'jobs#pending_payment',
    'services#index',
    'payments#index'
  ].freeze

  included do
    attr_accessor :scope, :records

    helper_method :current_path, :decorator, :sorting_direction, :sorting_param, :permitted_filter_params
  end

  def indexable(scope, needs_search_value: true) # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
    return empty_result if !present_search? && needs_search_value

    @scope = scope
    yield if block_given?
    @scope = @scope.full_text_search(params.dig(:search, :value), match: :all) if params.dig(:search, :value).present?

    @records = @scope.page(page).per(page_length).order_by(sorting_attr => sorting_direction)

    respond_to do |format|
      format.html
      format.json do
        render json: {
          draw: params[:draw],
          recordsFiltered: @scope.count,
          recordsTotal: records.count,
          data: decorated
        }
      end
    end
  end

  def searchable(scope)
    @scope = scope

    @records = @scope.search(
      query: lambda do |q, filter|
        query = (q and q[:value].present?) ? { match: { full_name: q[:value] } } : { match_all: {} }

        return { filtered: { query:, filter: } } if filter

        query
      end.call(params[:search], @filter),
      sort: [{ sorting_attr => { order: sorting_direction } }],
      size: page_length,
      from: page_length * (page - 1)
    )

    respond_to do |format|
      format.html
      format.json do
        render json: {
          draw: params[:draw],
          recordsFiltered: @scope.count,
          recordsTotal: records.count,
          data: decorated
        }
      end
    end
  end

  def empty_result
    @records = model.none.page(page).per(page_length)

    respond_to do |format|
      format.html
      format.json do
        render json: {
          draw: params[:draw],
          recordsFiltered: 0,
          recordsTotal: 0,
          data: []
        }
      end
    end
  end

  def current_path(override = {})
    url_for params: params.permit(:page, :length, search: :value, filter: permitted_filter_params).merge(override)
  end

  def present_search?
    params.dig(:search, :value)&.sub(/^beta: ?/, '').present? ||
      SEARCH_FIELDS.find { |field| params[field].present? } ||
      ACTIONS_TO_SHOW_SEARCH_RESULT.include?("#{controller_name}##{action_name}")
  end

  def decorated
    records.map { |r| decorator.decorate(r) }
  end

  def decorator
    "#{model}Decorator".constantize
  end

  def page
    return params[:page].to_i if params[:page].present?

    (params[:start].to_i / page_length) + 1
  end

  def page_length
    params[:length] ? params[:length].to_i : 10
  end

  def sorting_attr
    decorator::COLUMNS[sorting_param || 0] || decorator::COLUMNS[0]
  end

  def sorting_direction
    params.dig(:order, :'0', :dir)&.to_sym || :desc
  end

  def sorting_param
    params.dig(:order, :'0', :column)&.to_i
  end

  private

  def permitted_filter_params
    {}
  end
end
