# frozen_string_literal: true

class IncidentsController < ApplicationController
  authorize_resource

  include Indexable

  before_action :find_job, only: %i[cancel delay no_show new create]
  before_action :find_incident, only: %i[show destroy]

  def model
    Incident
  end

  def index
    needs_search_value = params.dig(:filter, :tasker_id, :value).blank? && params.dig(:filter, :job_id, :value).blank?

    indexable(scope, needs_search_value:) do
      if params.dig(:filter, :tasker_id, :value).present?
        @scope = @scope.where(tasker_id: params.dig(:filter, :tasker_id, :value))
      end

      if params.dig(:filter, :job_id, :value).present?
        @scope = @scope.where(job_id: params.dig(:filter, :job_id, :value))
      end
    end
  end

  def new
    @incident = Incident.new type: params[:type], job: @job, job_tasker_id: @job_tasker.id, tasker: @tasker, admin_user_id: current_admin_user.id
    render layout: false
  end

  def create
    service.send type
    redirect_to job_path(@job), flash: { success: t(:create_success, scope: :incidents) }
  end

  def show
    return redirect_to action: :index if !@incident.is_a?(Incident)
  end

  def cancel
    service.cancel
    redirect_to job_path(@job), flash: { success: t(:cancel_success, scope: :incidents) }
  end

  def destroy
    feedback = Feedback.find_by(incident_id: @incident.id)
    if feedback
      redirect_back(
        flash: { error: t(:feedback_exists, scope: :incidents, feedback_id: feedback.id) },
        fallback_location: root_path
      )
    else
      @incident.destroy(current_admin_user)
      redirect_back(
        flash: { success: t(:incident_removed, scope: :incidents) },
        fallback_location: root_path
      )
    end
  end

  private

  # Parameters attributes permitted to use by {Indexable#current_path}.
  #
  def permitted_filter_params
    { tasker_id: %i[type value], job_id: %i[type value] }
  end

  def type
    incident_params[:type].to_sym
  end

  def scope
    @scope = model.includes [:job, :tasker]
    @scope = @scope.where(tasker_id: params[:tasker_id]) if params[:tasker_id]
    @scope = @scope.where(job_id:    params[:job_id])    if params[:job_id]
    @scope
  end

  def service
    IncidentService.new @job, @job_tasker, @tasker, incident_params.to_h.merge(admin_user: current_admin_user)
  end

  def find_job
    @job        = Job.find(params[:job_id]       || params[:incident][:job_id])
    @tasker     = Tasker.find(params[:tasker_id] || params[:incident][:tasker_id])
    @job_tasker = @job.job_taskers.find_by(tasker_id: @tasker.id)
  end

  def find_incident
    @incident = Incident.find(params[:id] || params[:incident_id])
  end

  def incident_params
    return {} unless params.has_key? :incident

    params.require(:incident).permit!
  end
end
