# frozen_string_literal: true

module AdminNotes
  # This controller is used to pin admin notes.
  #
  class PinsController < ApplicationController
    authorize_resource class: 'AdminNote'

    before_action :set_admin_note

    def create
      authorize! :update, @admin_note
      @admin_note.update(pinned_at: Time.zone.now)
      redirect_back fallback_location: root_path, flash: { success: t('admin_note.pinned') }
    end

    def destroy
      authorize! :update, @admin_note
      @admin_note.update(pinned_at: nil)
      redirect_back fallback_location: root_path, flash: { success: t('admin_note.unpinned') }
    end

    private

    def set_admin_note
      @admin_note = AdminNote.find(params[:admin_note_id])
    end
  end
end
