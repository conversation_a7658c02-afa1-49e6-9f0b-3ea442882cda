# frozen_string_literal: true

# rubocop:disable RSpec/MultipleMemoizedHelpers

require 'spec_helper'

describe Workers::PaymentStatusUpdater do
  let!(:tasker) { create :tasker }
  let!(:entry_one) { create :payment_entry, tasker:, payout_state: 'processing' }
  let!(:entry_two) { create :payment_entry, tasker:, payout_state: 'processing' }
  let(:queue_has_messages) do
    instance_double Google::Apis::PubsubV1::PullResponse, received_messages: [
      instance_double(Google::Apis::PubsubV1::ReceivedMessage,
                      ack_id: '1',
                      message: instance_double(Google::Apis::PubsubV1::Message,
                                               data: {
                                                 entry_id: entry_one.id.to_s, status: 'success',
                                                 bank_receipt_url: 'BANK_RECEIPT_URL'
                                               }.to_json)),
      instance_double(Google::Apis::PubsubV1::ReceivedMessage,
                      ack_id: '2',
                      message: instance_double(Google::Apis::PubsubV1::Message,
                                               data: {
                                                 entry_id: entry_two.id.to_s, status: 'fail'
                                               }.to_json))
    ]
  end
  let(:queue_is_empty) { instance_double Google::Apis::PubsubV1::PullResponse, received_messages: [] }
  let(:refunder_instance_for_entry_one) { instance_double Payout::Statement::Refunder, refund: true }
  let(:refunder_instance_for_entry_two) { instance_double Payout::Statement::Refunder, refund: true }

  before do
    allow(Parafuzo::Core::Queue).to receive(:pull).and_return(queue_has_messages, queue_is_empty)
    allow(Parafuzo::Core::Queue).to receive(:ack)
    allow(Payout::Statement::Refunder).to receive(:new).with(entry_one).and_return(refunder_instance_for_entry_one)
    allow(Payout::Statement::Refunder).to receive(:new).with(entry_two).and_return(refunder_instance_for_entry_two)
    described_class.perform
  end

  context 'when process the queue' do
    it { expect(Parafuzo::Core::Queue).to have_received(:pull).twice.with('payments-on-status-update-poseidon', 128) }

    it { expect(Parafuzo::Core::Queue).to have_received(:ack).once.with('payments-on-status-update-poseidon', %w[1 2]) }
  end

  context 'when process a success payment' do
    it { expect(refunder_instance_for_entry_one).not_to have_received(:refund) }

    it 'updates the entry to accepted' do
      expect(entry_one.reload.payout_state).to eq 'accepted'
    end

    it 'updates the bank receipt url' do
      expect(entry_one.reload.bank_receipt_url).to eq 'BANK_RECEIPT_URL'
    end
  end

  context 'when process a fail payment' do
    it { expect(refunder_instance_for_entry_two).to have_received(:refund) }

    it 'updates the entry to rejected' do
      expect(entry_two.reload.payout_state).to eq 'rejected'
    end
  end

  context 'when the entry cannot be changed' do
    let!(:entry_one) { create :payment_entry, tasker:, payout_state: 'rejected' }

    it 'does not updates the entry' do
      expect(entry_one.reload.payout_state).to eq 'rejected'
    end
  end
end

# rubocop:enable RSpec/MultipleMemoizedHelpers
