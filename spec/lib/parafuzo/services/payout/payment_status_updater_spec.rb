# frozen_string_literal: true

require 'spec_helper'

require_relative '../../../../../lib/parafuzo/services/payout/payment_status_updater'

describe Services::Payout::PaymentStatusUpdater do
  subject(:call) { described_class.process(data) }

  let!(:entry) { create :payment_entry, payout_state: 'processing', gateway: :transfeera }

  context 'when successful payment is received' do
    let(:data) { { 'entry_id' => entry.id.to_s, 'status' => 'success', 'bank_receipt_url' => 'BANK_RECEIPT_URL' } }

    it { expect { call }.to(change { entry.reload.payout_state }.to('accepted')) }

    it { expect { call }.to(change { entry.reload.bank_receipt_url }.to('BANK_RECEIPT_URL')) }

    it { expect { call }.not_to(change { entry.reload.gateway }) }
  end

  context 'when gateway is received from service' do
    let(:data) { { 'entry_id' => entry.id.to_s, 'status' => 'success', 'gateway' => 'woovi' } }

    it { expect { call }.to(change { entry.reload.gateway }.to(:woovi)) }

    context 'when payment failed' do
      let(:data) { { 'entry_id' => entry.id.to_s, 'status' => 'fail', 'gateway' => 'woovi' } }

      it { expect { call }.to(change { entry.reload.gateway }.to(:woovi)) }
    end
  end

  context 'when failed payment is received' do
    let(:data) { { 'entry_id' => entry.id.to_s, 'status' => 'fail' } }
    let(:refunder) { instance_double Payout::Statement::Refunder }

    before do
      allow(Payout::Statement::Refunder).to receive(:new).with(entry).and_return refunder
      allow(refunder).to receive(:refund)
    end

    it { expect { call }.to(change { entry.reload.payout_state }.to('rejected')) }
    it { expect { call }.not_to(change { entry.reload.gateway }) }

    it 'calls refunder', :aggregate_failures do
      call

      expect(Payout::Statement::Refunder).to have_received(:new).with(entry)
      expect(refunder).to have_received(:refund).with('Pagamento rejeitado.')
    end
  end

  context 'when payment is already accepted' do
    let(:data) { { 'entry_id' => entry.id.to_s, 'status' => 'success', 'gateway' => 'woovi' } }
    let!(:entry) { create :payment_entry, payout_state: 'accepted', gateway: :transfeera }

    it { expect { call }.not_to(change { entry.reload.payout_state }) }
    it { expect { call }.not_to(change { entry.reload.gateway }) }
  end

  context 'when payment is already rejected' do
    let(:data) { { 'entry_id' => entry.id.to_s, 'status' => 'failed', 'gateway' => 'woovi' } }
    let!(:entry) { create :payment_entry, payout_state: 'rejected', gateway: :transfeera }

    it { expect { call }.not_to(change { entry.reload.payout_state }) }
    it { expect { call }.not_to(change { entry.reload.gateway }) }
  end
end
