# frozen_string_literal: true

require 'spec_helper'

require_relative '../../../../../lib/parafuzo/services/taskers/history_job'

RSpec.describe Taskers::HistoryJob do
  subject(:compute_job) do
    described_class.new(job_before: data['fullDocumentBeforeChange'], job_after: data['fullDocument']).compute
  end

  let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/assigned_job.json') }
  let!(:first_tasker) { create :tasker, :with_all_services, id: '63051cbf91fff500094f0b47' }
  let!(:second_tasker) { create :tasker, :with_all_services, id: '63052f4491fff500094f0b53' }
  let!(:job) { create :job, attributes: data['fullDocument'] }
  let(:order) { create :order, id: data['fullDocument']['order_id'], user_id: data['fullDocument']['user_id'] }

  before { create :user, id: data['fullDocument']['user_id'] }

  context 'when the job gets assigned' do
    it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }
  end

  context 'when the job gets created' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/created_job.json') }

    it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }
  end

  context 'when the job gets completed by two taskers' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/completed_job.json') }

    it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(0).to(2) }
    it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(1) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
    it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(1) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

    it 'creates a tasker history job for the first tasker' do # rubocop:disable RSpec/ExampleLength
      compute_job

      expect(TaskerHistoryJob.find_by(tasker: first_tasker, job: job)).to have_attributes(
        'fidelized_by_id' => nil,
        'fidelized_at' => nil,
        'fidelized_by_job_ids' => [],
        'job_date' => job.date,
        'performed_by_preferential' => job.job_taskers.first.performed_by_preferential,
        'subscription' => job.subscription
      )
    end

    it 'creates a tasker history job for the second tasker' do # rubocop:disable RSpec/ExampleLength
      compute_job

      expect(TaskerHistoryJob.find_by(tasker: second_tasker, job: job)).to have_attributes(
        'fidelized_by_id' => nil,
        'fidelized_at' => nil,
        'fidelized_by_job_ids' => [],
        'job_date' => job.date,
        'performed_by_preferential' => job.job_taskers.last.performed_by_preferential,
        'subscription' => job.subscription
      )
    end

    context 'when there is a job to be fidelized for one of the taskers' do
      subject(:fidelized_job) do
        create :job, :completed, :with_one_job_tasker, tasker: second_tasker, order: order, date: (job.date - 30.days)
      end

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: second_tasker,
                                    job_date: fidelized_job.date,
                                    performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(1).to(3) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(1) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(1) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_fidelity_points }.by(1) }

      it 'fidelizes the previous history job of the tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.find_by(tasker: second_tasker, job: fidelized_job)).to have_attributes(
          fidelized_by_id: job.id,
          fidelized_by_job_ids: [job.id],
          fidelized_at: job.date,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end

      context 'when there is a previous history job of the same user' do
        it 'does not fidelize older history jobs' do
          history_job = create :tasker_history_job, user: order.user, job_date: fidelized_job.date - 1.day
          compute_job

          expect(history_job.reload).to have_attributes fidelized_by: nil, fidelized_at: nil, fidelized_by_job_ids: []
        end
      end
    end

    context 'when the history job is already fidelized' do
      subject(:fidelized_job) do
        create :job, :completed, :with_one_job_tasker, tasker: second_tasker, order: order, date: (job.date - 30.days)
      end

      let(:other_job) { create :job, :completed, date: job.date }

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: second_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by_jobs: [other_job],
                                    performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(1).to(3) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(1) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(1) }
      it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

      it 'fidelizes the previous history job of the tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.find_by(tasker: second_tasker, job: fidelized_job)).to have_attributes(
          fidelized_by_id: job.id,
          fidelized_by_job_ids: [other_job.id, job.id],
          fidelized_at: job.date,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end
    end

    context 'when there are jobs to be fidelized by both taskers' do
      subject(:fidelized_job) do
        create :job, :completed, job_taskers: [build(:job_tasker, tasker: first_tasker),
                                               build(:job_tasker, tasker: second_tasker)],
                                 order: order,
                                 date: (job.date - 30.days)
      end

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: first_tasker,
                                    job_date: fidelized_job.date,
                                    performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential

        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: second_tasker,
                                    job_date: fidelized_job.date,
                                    performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(2).to(4) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(1) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_fidelity_points }.by(1) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(1) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_fidelity_points }.by(1) }

      it 'fidelizes the previous history job of the first tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.find_by(tasker: first_tasker, job: fidelized_job)).to have_attributes(
          fidelized_by_id: job.id,
          fidelized_by_job_ids: [job.id],
          fidelized_at: job.date,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end

      it 'fidelizes the previous history job of the second tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.find_by(tasker: second_tasker, job: fidelized_job)).to have_attributes(
          fidelized_by_id: job.id,
          fidelized_by_job_ids: [job.id],
          fidelized_at: job.date,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end

      context 'when there is a previous history job of the same user' do
        it 'does not fidelize older history jobs' do
          history_job = create :tasker_history_job, user: order.user, job_date: fidelized_job.date - 1.day
          compute_job

          expect(history_job.reload).to have_attributes fidelized_by: nil, fidelized_at: nil
        end
      end
    end

    context 'when the history jobs are already fidelized' do
      subject(:fidelized_job) do
        create :job, :completed, job_taskers: [build(:job_tasker, tasker: first_tasker),
                                               build(:job_tasker, tasker: second_tasker)],
                                 order: order,
                                 date: (job.date - 30.days)
      end

      let(:other_job) { create :job, :completed, date: job.date }

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: first_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by_jobs: [other_job],
                                    performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential

        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: second_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by_jobs: [other_job],
                                    performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(2).to(4) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(1) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(1) }
      it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

      it 'fidelizes the previous history job of the first tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.find_by(tasker: first_tasker, job: fidelized_job)).to have_attributes(
          fidelized_by_id: job.id,
          fidelized_by_job_ids: [other_job.id, job.id],
          fidelized_at: job.date,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end

      it 'fidelizes the previous history job of the second tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.find_by(tasker: second_tasker, job: fidelized_job)).to have_attributes(
          fidelized_by_id: job.id,
          fidelized_by_job_ids: [other_job.id, job.id],
          fidelized_at: job.date,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end
    end

    context 'when the job to be fidelized was over 90 days ago' do
      subject(:fidelized_job) do
        create :job, :completed, :with_one_job_tasker, tasker: first_tasker, order: order, date: (job.date - 91.days)
      end

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: second_tasker,
                                    job_date: fidelized_job.date,
                                    performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(1).to(3) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(1) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(1) }
      it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

      it 'does not fidelize the previous history job' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.first).to have_attributes(
          tasker_id: second_tasker.id,
          job_id: fidelized_job.id,
          fidelized_by_id: nil,
          fidelized_by_job_ids: [],
          fidelized_at: nil,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end
    end

    context 'when there was two jobs to be fidelized on the same day' do
      let(:fidelized_job) do
        create :job, :cleaning, :completed, order: order, date: (job.date - 7.days).change(hour: 10)
      end
      let(:other_fidelized_job) do
        create :job, :ironing, :completed, order: order, date: (job.date - 7.days).change(hour: 11)
      end
      let(:third_tasker) { create :tasker, :with_all_services }
      let(:fourth_tasker) { create :tasker, :with_all_services }

      before do
        create :tasker_history_job, job: fidelized_job, user: order.user, tasker: third_tasker
        create :tasker_history_job, job: other_fidelized_job, user: order.user, tasker: fourth_tasker
      end

      it 'fidelized the tasker history job' do
        compute_job

        expect(TaskerHistoryJob.find_by(job: fidelized_job, tasker: third_tasker)).to have_attributes(
          fidelized_by_job_ids: [job.id]
        )
      end

      it 'fidelized the other tasker history job' do
        compute_job

        expect(TaskerHistoryJob.find_by(job: other_fidelized_job, tasker: fourth_tasker)).to have_attributes(
          fidelized_by_job_ids: [job.id]
        )
      end

      it 'changes tasker fidelity points' do
        expect { compute_job }.to change { third_tasker.reload.history.partial_fidelity_points }.by(1)
      end

      it 'changes other tasker fidelity points' do
        expect { compute_job }.to change { fourth_tasker.reload.history.partial_fidelity_points }.by(1)
      end
    end

    context 'when there was two jobs to be fidelized on the same day but they were already fidelized' do
      let(:fidelized_job) do
        create :job, :cleaning, :completed, order: order, date: (job.date - 7.days).change(hour: 10)
      end
      let(:other_fidelized_job) do
        create :job, :ironing, :completed, order: order, date: (job.date - 7.days).change(hour: 11)
      end
      let(:third_tasker) { create :tasker, :with_all_services }
      let(:fourth_tasker) { create :tasker, :with_all_services }

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: third_tasker,
                                    fidelized_by_jobs: [create(:job, :completed, date: fidelized_job.date)]

        create :tasker_history_job, job: other_fidelized_job,
                                    user: order.user,
                                    tasker: fourth_tasker,
                                    fidelized_by_jobs: [create(:job, :completed, date: fidelized_job.date)]
      end

      it 'fidelized the tasker history job' do
        compute_job

        expect(
          TaskerHistoryJob.find_by(job: fidelized_job, tasker: third_tasker).fidelized_by_jobs.count
        ).to eq(2)
      end

      it 'fidelized the other tasker history job' do
        compute_job

        expect(
          TaskerHistoryJob.find_by(job: other_fidelized_job, tasker: fourth_tasker).fidelized_by_jobs.count
        ).to eq(2)
      end

      it 'changes tasker fidelity points' do
        expect { compute_job }.not_to(change { third_tasker.reload.history.partial_fidelity_points })
      end

      it 'changes other tasker fidelity points' do
        expect { compute_job }.not_to(change { fourth_tasker.reload.history.partial_fidelity_points })
      end
    end

    context 'when an exception is raised' do
      before { allow(TaskerHistoryJob).to receive(:create).and_raise StandardError }

      it { expect { compute_job }.to raise_error(StandardError).and(not_change(TaskerHistoryJob, :count)) }
    end
  end

  context 'when the job gets uncompleted with two taskers' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/uncompleted_job.json') }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '63051cbf91fff500094f0b47',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end
    let!(:second_tasker) do
      create :tasker,
             :with_all_services,
             id: '63052f4491fff500094f0b53',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, tasker_id: first_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.first.performed_by_preferential,
                                  subscription: job.subscription

      create :tasker_history_job, tasker_id: second_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.last.performed_by_preferential,
                                  subscription: job.subscription
    end

    it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(2).to(0) }
    it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
    it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(-1) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

    context 'when there is a job that was fidelized for one of the taskers' do
      subject(:fidelized_job) do
        create :job, :completed, :with_one_job_tasker, tasker: second_tasker, order: order, date: (job.date - 30.days)
      end

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: second_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by: job,
                                    fidelized_by_jobs: [job],
                                    fidelized_at: job.date,
                                    performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(3).to(1) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_fidelity_points }.by(-1) }

      it 'unfidelizes the previous history job of the tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.first).to have_attributes(
          tasker_id: second_tasker.id,
          job_id: fidelized_job.id,
          fidelized_by_id: nil,
          fidelized_by_job_ids: [],
          fidelized_at: nil,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end
    end

    context 'when the history job was fidelized by two jobs' do
      subject(:fidelized_job) do
        create :job, :completed, :with_one_job_tasker, tasker: second_tasker, order: order, date: (job.date - 30.days)
      end

      let(:other_job) { create :job, :completed, date: job.date }

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: second_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by_jobs: [job, other_job],
                                    performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(3).to(1) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

      it 'removes the uncompleted job from the history job of the tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.find_by(tasker: second_tasker, job: fidelized_job)).to have_attributes(
          fidelized_by_job_ids: [other_job.id],
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end
    end

    context 'when there are jobs that were fidelized for both taskers' do
      subject(:fidelized_job) do
        create :job, :completed, job_taskers: [build(:job_tasker, tasker: first_tasker),
                                               build(:job_tasker, tasker: second_tasker)],
                                 order: order,
                                 date: (job.date - 30.days)
      end

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: first_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by: job,
                                    fidelized_by_jobs: [job],
                                    fidelized_at: job.date,
                                    performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential

        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: second_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by: job,
                                    fidelized_by_jobs: [job],
                                    fidelized_at: job.date,
                                    performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(4).to(2) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_fidelity_points }.by(-1) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_fidelity_points }.by(-1) }

      it 'unfidelizes the previous history job of the first tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.first).to have_attributes(
          tasker_id: first_tasker.id,
          job_id: fidelized_job.id,
          fidelized_by_id: nil,
          fidelized_by_job_ids: [],
          fidelized_at: nil,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end

      it 'unfidelizes the previous history job of the second tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.last).to have_attributes(
          tasker_id: second_tasker.id,
          job_id: fidelized_job.id,
          fidelized_by_id: nil,
          fidelized_by_job_ids: [],
          fidelized_at: nil,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end
    end

    context 'when the history jobs are fidelized by two jobs' do
      subject(:fidelized_job) do
        create :job, :completed, job_taskers: [build(:job_tasker, tasker: first_tasker),
                                               build(:job_tasker, tasker: second_tasker)],
                                 order: order,
                                 date: (job.date - 30.days)
      end

      let(:other_job) { create :job, :completed, date: job.date }

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: first_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by_jobs: [job, other_job],
                                    performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential

        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: second_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by_jobs: [job, other_job],
                                    performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(4).to(2) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

      it 'removes the uncompleted job from the history job of the first tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.find_by(tasker: first_tasker, job: fidelized_job)).to have_attributes(
          fidelized_by_id: nil,
          fidelized_by_job_ids: [other_job.id],
          fidelized_at: nil,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end

      it 'removes the uncompleted job from the history job of the second tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.find_by(tasker: second_tasker, job: fidelized_job)).to have_attributes(
          fidelized_by_id: nil,
          fidelized_by_job_ids: [other_job.id],
          fidelized_at: nil,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end
    end

    context 'when an exception is raised' do
      before { allow(TaskerHistoryJob).to receive(:find_by).and_raise StandardError }

      it { expect { compute_job }.to raise_error(StandardError).and(not_change(TaskerHistoryJob, :count)) }
    end
  end

  context 'when the job gets cancelled after being completed by two taskers' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/cancelled_completed_job.json') }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '6303ec4b6ac4c0000a20d33a',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end
    let!(:second_tasker) do
      create :tasker,
             :with_all_services,
             id: '6304df9991fff500094f0b00',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, tasker_id: first_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.first.performed_by_preferential,
                                  subscription: job.subscription

      create :tasker_history_job, tasker_id: second_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.last.performed_by_preferential,
                                  subscription: job.subscription
    end

    it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(2).to(0) }
    it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
    it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(-1) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

    context 'when there is a job that was fidelized for one of the taskers' do
      subject(:fidelized_job) do
        create :job, :completed, :with_one_job_tasker, tasker: second_tasker, order: order, date: (job.date - 30.days)
      end

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: second_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by: job,
                                    fidelized_by_jobs: [job],
                                    fidelized_at: job.date,
                                    performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(3).to(1) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_fidelity_points }.by(-1) }

      it 'unfidelizes the previous tasker history job' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.first).to have_attributes(
          tasker_id: second_tasker.id,
          job_id: fidelized_job.id,
          fidelized_by_id: nil,
          fidelized_by_job_ids: [],
          fidelized_at: nil,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end
    end

    context 'when the history job was fidelized by two jobs' do
      subject(:fidelized_job) do
        create :job, :completed, :with_one_job_tasker, tasker: second_tasker, order: order, date: (job.date - 30.days)
      end

      let(:other_job) { create :job, :completed, date: job.date }

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: second_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by_jobs: [job, other_job],
                                    performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(3).to(1) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

      it 'removes the cancelled job from the history job of the tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.find_by(tasker: second_tasker, job: fidelized_job)).to have_attributes(
          fidelized_by_job_ids: [other_job.id],
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end
    end

    context 'when an exception is raised' do
      before { allow(TaskerHistoryJob).to receive(:find_by).and_raise StandardError }

      it { expect { compute_job }.to raise_error(StandardError).and(not_change(TaskerHistoryJob, :count)) }
    end
  end

  context 'when the cancelled job is not the latest' do
    subject(:newer_job) do
      create :job, :completed, :with_one_job_tasker, tasker: second_tasker, order: order, date: (job.date + 1.day)
    end

    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/cancelled_completed_job.json') }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '6303ec4b6ac4c0000a20d33a',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end
    let!(:second_tasker) do
      create :tasker,
             :with_all_services,
             id: '6304df9991fff500094f0b00',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, tasker_id: first_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  fidelized_by: newer_job,
                                  fidelized_by_jobs: [newer_job],
                                  fidelized_at: newer_job.date

      create :tasker_history_job, tasker_id: second_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  fidelized_by: newer_job,
                                  fidelized_by_jobs: [newer_job],
                                  fidelized_at: newer_job.date

      create :tasker_history_job, tasker_id: second_tasker.id,
                                  job_id: newer_job.id,
                                  job_date: newer_job.date
    end

    it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(3).to(1) }
    it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
    it { expect { compute_job }.to change { first_tasker.reload.history.partial_fidelity_points }.by(-1) }
    it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(-1) }
    it { expect { compute_job }.to change { second_tasker.reload.history.partial_fidelity_points }.by(-1) }
  end

  context 'when the job gets cancelled after being paid' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/cancelled_paid_job.json') }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '6320c2f0719d66000a1820d7',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, tasker_id: first_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.first.performed_by_preferential,
                                  subscription: job.subscription
    end

    it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(1).to(0) }
    it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }

    context 'when there is a job that was fidelized' do
      subject(:fidelized_job) do
        create :job, :completed, :with_one_job_tasker, tasker: first_tasker, order: order, date: (job.date - 30.days)
      end

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: first_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by: job,
                                    fidelized_by_jobs: [job],
                                    fidelized_at: job.date,
                                    performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(2).to(1) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_fidelity_points }.by(-1) }

      it 'unfidelizes the previous tasker history job' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.first).to have_attributes(
          tasker_id: first_tasker.id,
          job_id: fidelized_job.id,
          fidelized_by_id: nil,
          fidelized_by_job_ids: [],
          fidelized_at: nil,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end
    end

    context 'when the history job was fidelized by two jobs' do
      subject(:fidelized_job) do
        create :job, :completed, :with_one_job_tasker, tasker: first_tasker, order: order, date: (job.date - 30.days)
      end

      let(:other_job) { create :job, :completed, date: job.date }

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: first_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by_jobs: [job, other_job],
                                    performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(2).to(1) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }

      it 'removes the cancelled job from the history job of the tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.find_by(tasker: first_tasker, job: fidelized_job)).to have_attributes(
          fidelized_by_job_ids: [other_job.id],
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end
    end

    context 'when an exception is raised' do
      before { allow(TaskerHistoryJob).to receive(:find_by).and_raise StandardError }

      it { expect { compute_job }.to raise_error(StandardError).and(not_change(TaskerHistoryJob, :count)) }
    end
  end

  context 'when the job gets cancelled after being invoiced' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/cancelled_invoiced_job.json') }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '63051cbf91fff500094f0b47',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, tasker_id: first_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.first.performed_by_preferential,
                                  subscription: job.subscription
    end

    it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(1).to(0) }
    it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }

    context 'when there is a job that was fidelized' do
      subject(:fidelized_job) do
        create :job, :completed, :with_one_job_tasker, tasker: first_tasker, order: order, date: (job.date - 30.days)
      end

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: first_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by: job,
                                    fidelized_by_jobs: [job],
                                    fidelized_at: job.date,
                                    performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(2).to(1) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_fidelity_points }.by(-1) }

      it 'unfidelizes the previous tasker history job' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.find_by(tasker: first_tasker, job: fidelized_job)).to have_attributes(
          fidelized_by_id: nil,
          fidelized_by_job_ids: [],
          fidelized_at: nil,
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.first.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end
    end

    context 'when the history job was fidelized by two jobs' do
      subject(:fidelized_job) do
        create :job, :completed, :with_one_job_tasker, tasker: first_tasker, order: order, date: (job.date - 30.days)
      end

      let(:other_job) { create :job, :completed, date: job.date }

      before do
        create :tasker_history_job, job: fidelized_job,
                                    user: order.user,
                                    tasker: first_tasker,
                                    job_date: fidelized_job.date,
                                    fidelized_by_jobs: [job, other_job],
                                    performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential
      end

      it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(2).to(1) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }

      it 'removes the cancelled job from the history job of the tasker' do # rubocop:disable RSpec/ExampleLength
        compute_job

        expect(TaskerHistoryJob.find_by(tasker: first_tasker, job: fidelized_job)).to have_attributes(
          fidelized_by_job_ids: [other_job.id],
          job_date: fidelized_job.date,
          performed_by_preferential: fidelized_job.job_taskers.last.performed_by_preferential,
          subscription: fidelized_job.subscription
        )
      end
    end

    context 'when an exception is raised' do
      before { allow(TaskerHistoryJob).to receive(:find_by).and_raise StandardError }

      it { expect { compute_job }.to raise_error(StandardError).and(not_change(TaskerHistoryJob, :count)) }
    end
  end

  context 'when the job gets skipped after being assigned' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/skipped_assigned_job.json') }
    let(:first_tasker) { create :tasker, :with_all_services, id: '63051cbf91fff500094f0b47' }

    it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
  end

  context 'when tasker gets removed from a completed job with two taskers' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/removed_tasker_completed_job.json') }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '6304df9991fff500094f0b00',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end
    let!(:second_tasker) do
      create :tasker,
             :with_all_services,
             id: '6303ec4b6ac4c0000a20d33a',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, tasker_id: first_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.first.performed_by_preferential,
                                  subscription: job.subscription

      create :tasker_history_job, tasker_id: second_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.last.performed_by_preferential,
                                  subscription: job.subscription
    end

    it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(2).to(1) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
    it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(-1) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

    it 'remains only the first tasker history job' do # rubocop:disable RSpec/ExampleLength
      compute_job

      expect(TaskerHistoryJob.first).to have_attributes(
        'tasker_id' => first_tasker.id,
        'job_id' => job.id,
        'fidelized_by_id' => nil,
        'fidelized_by_job_ids' => [],
        'fidelized_at' => nil,
        'job_date' => job.date,
        'performed_by_preferential' => job.job_taskers.first.performed_by_preferential,
        'subscription' => job.subscription
      )
    end

    context 'when an exception is raised' do
      before { allow(TaskerHistoryJob).to receive(:find_by).and_raise StandardError }

      it { expect { compute_job }.to raise_error(StandardError).and(not_change(TaskerHistoryJob, :count)) }
    end
  end

  context 'when tasker gets exchanged by another tasker in a completed job with two taskers' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/exchanged_tasker_completed_job.json') }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '6304df9991fff500094f0b00',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end
    let!(:second_tasker) do
      create :tasker,
             :with_all_services,
             id: '67f46a0266775b07cf1d5d49',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end
    let!(:third_tasker) do
      create :tasker,
             :with_all_services,
             id: '6303ec4b6ac4c0000a20d33a',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, tasker_id: first_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.first.performed_by_preferential,
                                  subscription: job.subscription

      create :tasker_history_job, tasker_id: third_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.last.performed_by_preferential,
                                  subscription: job.subscription
    end

    it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
    it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(1) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }
    it { expect { compute_job }.to change { third_tasker.reload.history.partial_completed_jobs }.by(-1) }
    it { expect { compute_job }.not_to(change { third_tasker.reload.history.partial_fidelity_points }) }

    it 'remains the first tasker history job' do # rubocop:disable RSpec/ExampleLength
      compute_job

      expect(TaskerHistoryJob.find_by(tasker: first_tasker, job: job)).to have_attributes(
        'fidelized_by_id' => nil,
        'fidelized_by_job_ids' => [],
        'fidelized_at' => nil,
        'job_date' => job.date,
        'performed_by_preferential' => job.job_taskers.first.performed_by_preferential,
        'subscription' => job.subscription
      )
    end

    it 'creates the included tasker a history job' do # rubocop:disable RSpec/ExampleLength
      compute_job

      expect(TaskerHistoryJob.find_by(tasker: second_tasker, job: job)).to have_attributes(
        'fidelized_by_id' => nil,
        'fidelized_by_job_ids' => [],
        'fidelized_at' => nil,
        'job_date' => job.date,
        'performed_by_preferential' => job.job_taskers.last.performed_by_preferential,
        'subscription' => job.subscription
      )
    end
  end

  context 'when job tasker gets removed from a paid job with two taskers' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/removed_job_tasker_paid_job.json') }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '6304df9991fff500094f0b00',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end
    let!(:second_tasker) do
      create :tasker,
             :with_all_services,
             id: '6303ec4b6ac4c0000a20d33a',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, tasker_id: first_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.first.performed_by_preferential,
                                  subscription: job.subscription

      create :tasker_history_job, tasker_id: second_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.last.performed_by_preferential,
                                  subscription: job.subscription
    end

    it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(2).to(1) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
    it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(-1) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

    it 'remains only the first tasker history job' do # rubocop:disable RSpec/ExampleLength
      compute_job

      expect(TaskerHistoryJob.first).to have_attributes(
        'tasker_id' => first_tasker.id,
        'job_id' => job.id,
        'fidelized_by_id' => nil,
        'fidelized_by_job_ids' => [],
        'fidelized_at' => nil,
        'job_date' => job.date,
        'performed_by_preferential' => job.job_taskers.first.performed_by_preferential,
        'subscription' => job.subscription
      )
    end

    context 'when an exception is raised' do
      before { allow(TaskerHistoryJob).to receive(:find_by).and_raise StandardError }

      it { expect { compute_job }.to raise_error(StandardError).and(not_change(TaskerHistoryJob, :count)) }
    end
  end

  context 'when job tasker gets removed from a completed job with one tasker' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/removed_job_tasker_completed_job.json') }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '6304df9991fff500094f0b00',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, tasker_id: first_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  subscription: job.subscription
    end

    it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(1).to(0) }
    it { expect { compute_job }.to change { first_tasker.reload.history.partial_completed_jobs }.by(-1) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
  end

  context 'when job tasker gets removed from a completed job with one tasker but there is no tasker history job' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/removed_job_tasker_completed_job.json') }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '6304df9991fff500094f0b00',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
  end

  context 'when the completed job with two taskers gets paid' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/paid_completed_job.json') }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '6304df9991fff500094f0b00',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end
    let!(:second_tasker) do
      create :tasker,
             :with_all_services,
             id: '6303ec4b6ac4c0000a20d33a',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, tasker_id: first_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.first.performed_by_preferential,
                                  subscription: job.subscription

      create :tasker_history_job, tasker_id: second_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.last.performed_by_preferential,
                                  subscription: job.subscription
    end

    it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

    it 'remains the first tasker history job' do # rubocop:disable RSpec/ExampleLength
      compute_job

      expect(TaskerHistoryJob.first).to have_attributes(
        'tasker_id' => first_tasker.id,
        'job_id' => job.id,
        'fidelized_by_id' => nil,
        'fidelized_by_job_ids' => [],
        'fidelized_at' => nil,
        'job_date' => job.date,
        'performed_by_preferential' => job.job_taskers.first.performed_by_preferential,
        'subscription' => job.subscription
      )
    end

    it 'remains the second tasker history job' do # rubocop:disable RSpec/ExampleLength
      compute_job

      expect(TaskerHistoryJob.last).to have_attributes(
        'tasker_id' => second_tasker.id,
        'job_id' => job.id,
        'fidelized_by_id' => nil,
        'fidelized_by_job_ids' => [],
        'fidelized_at' => nil,
        'job_date' => job.date,
        'performed_by_preferential' => job.job_taskers.last.performed_by_preferential,
        'subscription' => job.subscription
      )
    end
  end

  context 'when the completed job turns into invoiced' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/invoiced_completed_job.json') }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '63051cbf91fff500094f0b47',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, tasker_id: first_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.first.performed_by_preferential,
                                  subscription: job.subscription
    end

    it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }

    it 'remains the tasker history job' do # rubocop:disable RSpec/ExampleLength
      compute_job

      expect(TaskerHistoryJob.first).to have_attributes(
        'tasker_id' => first_tasker.id,
        'job_id' => job.id,
        'fidelized_by_id' => nil,
        'fidelized_by_job_ids' => [],
        'fidelized_at' => nil,
        'job_date' => job.date,
        'performed_by_preferential' => job.job_taskers.first.performed_by_preferential,
        'subscription' => job.subscription
      )
    end
  end

  context 'when the tasker gets included in a completed job that already has a tasker' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/included_tasker_completed_job.json') }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '6304df9991fff500094f0b00',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end
    let!(:second_tasker) do
      create :tasker,
             :with_all_services,
             id: '6303ec4b6ac4c0000a20d33a',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, tasker_id: first_tasker.id,
                                  job_id: job.id,
                                  job_date: job.date,
                                  performed_by_preferential: job.job_taskers.first.performed_by_preferential,
                                  subscription: job.subscription
    end

    it { expect { compute_job }.to change(TaskerHistoryJob, :count).from(1).to(2) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
    it { expect { compute_job }.to change { second_tasker.reload.history.partial_completed_jobs }.by(1) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

    it 'creates the inserted tasker a history job' do # rubocop:disable RSpec/ExampleLength
      compute_job

      expect(TaskerHistoryJob.last).to have_attributes(
        'tasker_id' => second_tasker.id,
        'job_id' => job.id,
        'fidelized_by_id' => nil,
        'fidelized_by_job_ids' => [],
        'fidelized_at' => nil,
        'job_date' => job.date,
        'performed_by_preferential' => job.job_taskers.last.performed_by_preferential,
        'subscription' => job.subscription
      )
    end

    context 'when an exception is raised' do
      before { allow(TaskerHistoryJob).to receive(:create).and_raise StandardError }

      it { expect { compute_job }.to raise_error(StandardError).and(not_change(TaskerHistoryJob, :count)) }
    end
  end

  context 'when the tasker gets included in a pending job' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/included_taskers_pending_job.json') }
    let(:first_tasker) { create :tasker, :with_all_services, id: '63051cbf91fff500094f0b47' }
    let(:second_tasker) { create :tasker, :with_all_services, id: '6320c2f0719d66000a1820d7' }

    it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
  end

  context 'when the tasker gets removed from an assigned job' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/removed_tasker_assigned_job.json') }
    let(:first_tasker) { create :tasker, :with_all_services, id: '63051cbf91fff500094f0b47' }
    let(:second_tasker) { create :tasker, :with_all_services, id: '6320c2f0719d66000a1820d7' }

    it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
  end

  context 'when the job goes from assigned to on_hold' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/on_hold_assigned_job.json') }
    let(:first_tasker) { create :tasker, :with_all_services, id: '6320c2f0719d66000a1820d7' }

    it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
  end

  context 'when the job does not have job_tasker at first' do
    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/included_job_tasker_pending_job.json') }

    it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
  end

  context 'when the job get its date changed after being completed with two taskers' do
    subject(:fidelized_job) do
      create :job, :completed, job_taskers: [build(:job_tasker, tasker: first_tasker),
                                             build(:job_tasker, tasker: second_tasker)],
                               order: order,
                               date: (job.date - 1.day)
    end

    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/date_updated_completed_job.json') }
    let!(:job) { create :job, attributes: data['fullDocumentBeforeChange'] }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '6320c2f0719d66000a1820d7',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end
    let!(:second_tasker) do
      create :tasker,
             :with_all_services,
             id: '63051cbf91fff500094f0b47',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, tasker: first_tasker,
                                  job: job,
                                  user: order.user,
                                  job_date: job.date

      create :tasker_history_job, tasker: second_tasker,
                                  job: job,
                                  user: order.user,
                                  job_date: job.date

      create :tasker_history_job, job: fidelized_job,
                                  user: order.user,
                                  tasker: first_tasker,
                                  job_date: fidelized_job.date,
                                  fidelized_by: job,
                                  fidelized_by_jobs: [job],
                                  fidelized_at: job.date

      create :tasker_history_job, job: fidelized_job,
                                  user: order.user,
                                  tasker: second_tasker,
                                  job_date: fidelized_job.date,
                                  fidelized_by: job,
                                  fidelized_by_jobs: [job],
                                  fidelized_at: job.date
    end

    it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

    it 'unfidelizes the history job for the first tasker' do
      compute_job

      expect(TaskerHistoryJob.find_by(job: fidelized_job, tasker: first_tasker)).to have_attributes(
        'fidelized_by_id' => nil, 'fidelized_by_job_ids' => [], 'fidelized_at' => nil
      )
    end

    it 'unfidelizes the history job for the other tasker' do
      compute_job

      expect(TaskerHistoryJob.find_by(job: fidelized_job, tasker: second_tasker)).to have_attributes(
        'fidelized_by_id' => nil, 'fidelized_by_job_ids' => [], 'fidelized_at' => nil
      )
    end

    it 'changes the job_date and fidelizes the history job for the first tasker' do
      compute_job

      expect(TaskerHistoryJob.find_by(job: job, tasker: first_tasker)).to have_attributes(
        'fidelized_by_id' => fidelized_job.id, 'fidelized_by_job_ids' => [fidelized_job.id],
        'fidelized_at' => fidelized_job.date, 'job_date' => job.date - 2.days
      )
    end

    it 'changes the job_date and fidelizes the history job for the other tasker' do
      compute_job

      expect(TaskerHistoryJob.find_by(job:, tasker: second_tasker)).to have_attributes(
        'fidelized_by_id' => fidelized_job.id, 'fidelized_by_job_ids' => [fidelized_job.id],
        'fidelized_at' => fidelized_job.date, 'job_date' => job.date - 2.days
      )
    end

    context 'when the fidelized job is older than the new job date' do
      subject(:fidelized_job) do
        create :job, :completed, job_taskers: [build(:job_tasker, tasker: first_tasker),
                                               build(:job_tasker, tasker: second_tasker)],
                                 order: order,
                                 date: (job.date - 3.days)
      end

      it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }
      it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_completed_jobs }) }
      it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_fidelity_points }) }

      it 'changes the fidelized_at of the fidelized history job for the first tasker' do
        compute_job

        expect(TaskerHistoryJob.find_by(job: fidelized_job, tasker: first_tasker)).to have_attributes(
          'fidelized_by_id' => job.id, 'fidelized_by_job_ids' => [job.id],
          'fidelized_at' => job.date - 2.days, 'job_date' => fidelized_job.date
        )
      end

      it 'changes the fidelized_at of the fidelized history job for the other tasker' do
        compute_job

        expect(TaskerHistoryJob.find_by(job: fidelized_job, tasker: second_tasker)).to have_attributes(
          'fidelized_by_id' => job.id, 'fidelized_by_job_ids' => [job.id],
          'fidelized_at' => job.date - 2.days, 'job_date' => fidelized_job.date
        )
      end

      it 'changes the job_date of the history job for the first tasker' do
        compute_job

        expect(TaskerHistoryJob.find_by(job: job, tasker: first_tasker)).to have_attributes(
          'fidelized_by_id' => nil, 'fidelized_by_job_ids' => [], 'fidelized_at' => nil, 'job_date' => job.date - 2.days
        )
      end

      it 'changes the job_date of the history job for the other tasker' do
        compute_job

        expect(TaskerHistoryJob.find_by(job: job, tasker: second_tasker)).to have_attributes(
          'fidelized_by_id' => nil, 'fidelized_by_job_ids' => [], 'fidelized_at' => nil, 'job_date' => job.date - 2.days
        )
      end
    end

    context 'when there were an older job to be fidelized' do
      let(:older_job) do
        create :job, :completed, job_taskers: [build(:job_tasker, tasker: first_tasker),
                                               build(:job_tasker, tasker: second_tasker)],
                                 order: order,
                                 date: (job.date - 3.days)
      end

      before do
        create :tasker_history_job, tasker: first_tasker,
                                    job: older_job,
                                    user: order.user,
                                    job_date: older_job.date

        create :tasker_history_job, tasker: second_tasker,
                                    job: older_job,
                                    user: order.user,
                                    job_date: older_job.date
      end

      it { expect { compute_job }.not_to change(TaskerHistoryJob, :count) }
      it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
      it { expect { compute_job }.to change { first_tasker.reload.history.partial_fidelity_points }.by(1) }
      it { expect { compute_job }.not_to(change { second_tasker.reload.history.partial_completed_jobs }) }
      it { expect { compute_job }.to change { second_tasker.reload.history.partial_fidelity_points }.by(1) }

      it 'unfidelizes the history job for the first tasker' do
        compute_job

        expect(TaskerHistoryJob.find_by(job: fidelized_job, tasker: first_tasker)).to have_attributes(
          'fidelized_by_id' => nil, 'fidelized_by_job_ids' => [], 'fidelized_at' => nil
        )
      end

      it 'unfidelizes the history job for the other tasker' do
        compute_job

        expect(TaskerHistoryJob.find_by(job: fidelized_job, tasker: second_tasker)).to have_attributes(
          'fidelized_by_id' => nil, 'fidelized_by_job_ids' => [], 'fidelized_at' => nil
        )
      end

      it 'fidelizes the old history job for the first tasker' do
        compute_job

        expect(TaskerHistoryJob.find_by(job: older_job, tasker: first_tasker)).to have_attributes(
          'fidelized_by_id' => job.id, 'fidelized_by_job_ids' => [job.id],
          'fidelized_at' => job.date - 2.days, 'job_date' => older_job.date
        )
      end

      it 'fidelizes the old history job for the other tasker' do
        compute_job

        expect(TaskerHistoryJob.find_by(job: older_job, tasker: second_tasker)).to have_attributes(
          'fidelized_by_id' => job.id, 'fidelized_by_job_ids' => [job.id],
          'fidelized_at' => job.date - 2.days, 'job_date' => older_job.date
        )
      end

      it 'changes the job_date and fidelizes the history job for the first tasker' do
        compute_job

        expect(TaskerHistoryJob.find_by(job: job, tasker: first_tasker)).to have_attributes(
          'fidelized_by_id' => fidelized_job.id, 'fidelized_by_job_ids' => [fidelized_job.id],
          'fidelized_at' => fidelized_job.date, 'job_date' => job.date - 2.days
        )
      end

      it 'changes the job_date and fidelizes the history job for the other tasker' do
        compute_job

        expect(TaskerHistoryJob.find_by(job: job, tasker: second_tasker)).to have_attributes(
          'fidelized_by_id' => fidelized_job.id, 'fidelized_by_job_ids' => [fidelized_job.id],
          'fidelized_at' => fidelized_job.date, 'job_date' => job.date - 2.days
        )
      end
    end
  end

  context 'when the job get its date changed while being assigned' do
    subject(:fidelized_job) do
      create :job, :completed, job_taskers: [build(:job_tasker, tasker: first_tasker)],
                               order: order,
                               date: (job.date - 2.days)
    end

    let(:data) { JSON.parse File.read('spec/fixtures/json/jobs/date_updated_assigned_job.json') }
    let!(:job) { create :job, attributes: data['fullDocumentBeforeChange'] }
    let!(:first_tasker) do
      create :tasker,
             :with_all_services,
             id: '6320c2f0719d66000a1820d7',
             history: build(:tasker_history, partial_completed_jobs: 2, partial_fidelity_points: 2)
    end

    before do
      create :tasker_history_job, job: fidelized_job,
                                  user: order.user,
                                  tasker: first_tasker,
                                  job_date: fidelized_job.date
    end

    it { expect { compute_job }.not_to change TaskerHistoryJob, :count }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_completed_jobs }) }
    it { expect { compute_job }.not_to(change { first_tasker.reload.history.partial_fidelity_points }) }

    it 'does nothing to existent tasker history job' do
      compute_job

      expect(TaskerHistoryJob.find_by(job: fidelized_job, tasker: first_tasker)).to have_attributes(
        'fidelized_by_id' => nil, 'fidelized_by_job_ids' => [], 'fidelized_at' => nil, 'job_date' => fidelized_job.date
      )
    end
  end
end
