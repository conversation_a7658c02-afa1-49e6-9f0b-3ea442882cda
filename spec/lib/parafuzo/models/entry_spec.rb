# frozen_string_literal: true

require 'model_helper'

RSpec.describe Entry, type: :model do
  let(:entry) { build(:entry, :credit, :by_admin) }

  it { is_expected.to have_field(:related_entries).of_type(Array).with_default_value_of([]) }
  it { is_expected.to have_field(:bank_receipt_url).of_type(String) }

  describe 'indexes' do
    it { is_expected.to have_index_for(deal_id: 1).with_options(background: true) }
    it { is_expected.to have_index_for(deleted_at: 1, tasker_id: 1, type: 1).with_options(background: true) }
    it { is_expected.to have_index_for(gateway: 1).with_options(background: true) }
    it { is_expected.to have_index_for(job_id: 1).with_options(background: true) }
    it { is_expected.to have_index_for(number: 1).with_options(background: true) }
    it { is_expected.to have_index_for(payment_correlation_id: 1).with_options(background: true) }
    it { is_expected.to have_index_for(tasker_id: 1).with_options(background: true) }

    it do
      expect(entry).to have_index_for(tasker_id: 1, deleted_at: 1, payment_correlation_id: 1,
                                      type: 1, created_at: 1, gateway: 1, job_id: 1).with_options(background: true)
    end
  end

  it { should be_a Parafuzo::Core::Notify }
  it { is_expected.to validate_presence_of(:actor) }
  it { is_expected.to validate_presence_of(:number) }
  it { is_expected.to validate_presence_of(:tasker) }
  it { is_expected.to validate_presence_of(:type) }
  it { is_expected.to validate_presence_of(:value) }
  it { is_expected.to validate_inclusion_of(:actor).to_allow(%i[admin user system]) }
  it { is_expected.to validate_inclusion_of(:gateway).to_allow(:stripe, :iugu, :transfeera, :woovi, nil) }
  it { is_expected.to validate_inclusion_of(:type).to_allow(%i[credit debit payment fee]) }
  it { is_expected.to validate_uniqueness_of(:number) }

  describe 'validates user presence' do
    context 'when actor is admin' do
      subject { build :entry, :by_admin }

      it { is_expected.not_to validate_presence_of(:user) }
    end

    context 'when actor is user' do
      subject { build :entry, :by_user }

      it { is_expected.to validate_presence_of(:user) }
    end

    context 'when actor is system' do
      subject { build :entry, :by_system }

      it { is_expected.not_to validate_presence_of(:user) }
    end
  end

  describe 'validates motive presence' do
    context 'when actor is admin and entry is a debit' do
      subject { build :entry, :debit, :by_admin }

      it { is_expected.to validate_presence_of(:motive) }
    end

    context 'when actor is admin and entry is a credit' do
      subject { build :entry, :credit, :by_admin }

      it { is_expected.to validate_presence_of(:motive) }
    end

    context 'when actor is user' do
      subject { build :entry, :credit, :by_user }

      it { is_expected.not_to validate_presence_of(:motive) }
    end

    context 'when actor is system' do
      subject { build :entry, :credit, :by_system }

      it { is_expected.not_to validate_presence_of(:motive) }
    end
  end

  describe 'validates gateway presence' do
    context 'when entry is a debit' do
      subject { build :entry, :debit }

      it { is_expected.to validate_presence_of(:gateway) }
    end

    context 'when entry is a credit' do
      subject { build :entry, :credit }

      it { is_expected.to validate_presence_of(:gateway) }
    end

    context 'when entry is a payment' do
      subject { build :entry, :payment }

      it { is_expected.not_to validate_presence_of(:gateway) }
    end

    context 'when entry is a fee' do
      subject { build :entry, :fee }

      it { is_expected.not_to validate_presence_of(:gateway) }
    end
  end

  context 'when failing validations' do
    it 'does not persist entry without adminuser reference' do
      expect { entry.save! }.to raise_error(Mongoid::Errors::Validations)
    end
  end

  describe '#refundable?' do
    let(:entry) { build(:entry, :credit, :by_admin) }

    context 'when refundable' do
      it { expect(entry).to be_refundable }
    end

    context 'when unrefundable by already refund' do
      let(:entry) { build(:entry, :credit, :by_admin, refunded_by: '12356') }

      it { expect(entry).not_to be_refundable }
    end

    context 'when unrefundable by not credit' do
      let(:entry) { build(:entry, :by_admin, type: :payment) }

      it { expect(entry).not_to be_refundable }
    end

    context 'when unrefundable by payment_correlation_id' do
      let(:entry) { build(:entry, :by_admin, payment_correlation_id: 'payment') }

      it 'cannot refund' do
        expect(entry).not_to be_refundable
      end
    end
  end

  describe '#related' do

    context 'when is a payment' do
      let(:type) { :payment }
      let(:entry) { create :entry, type, :by_system, number: 42 }

      before do
        create :entry, :credit, :by_system, payment_correlation_id: 42
      end

      it 'when is payment correlation id' do
        expect(entry.related.size).to eq(1)
      end
    end
  end

  describe '#denormalized_related' do
    let(:related_entry_0) { create :entry, :credit, :by_system }
    let(:related_entry_1) { create :entry, :credit, :by_system }
    let(:payment)         { create :entry, :payment, :by_system, related_entries: [related_entry_0.id, related_entry_1.id] }

    it 'when return related entries' do
      expect(payment.denormalized_related.map(&:id)).to eq([related_entry_0.id, related_entry_1.id])
    end
  end

  context 'when type is credit and there is no gateway' do
    let(:entry) { build :credit_entry, gateway: gateway }
    let(:gateway) { nil }

    it { expect(entry).not_to be_valid }

    context 'when gateway is iugu' do
      let(:gateway) { :iugu }

      it { expect(entry).to be_valid }
    end

    context 'when gateway is stripe' do
      let(:gateway) { :stripe }

      it { expect(entry).to be_valid }
    end
  end

  context 'when type is debit and there is no gateway' do
    let(:entry) { build :debit_entry, gateway: gateway }
    let(:gateway) { nil }

    it { expect(entry).not_to be_valid }

    context 'when gateway is iugu' do
      let(:gateway) { :iugu }

      it { expect(entry).to be_valid }
    end

    context 'when gateway is stripe' do
      let(:gateway) { :stripe }

      it { expect(entry).to be_valid }
    end
  end

  context 'when type is payment and there is no gateway' do
    let(:entry) { build :payment_entry, gateway: gateway }
    let(:gateway) { nil }

    it { expect(entry).to be_valid }

    context 'when there is a gateway' do
      let(:gateway) { :iugu }

      it { expect(entry).to be_valid }
    end
  end
end
