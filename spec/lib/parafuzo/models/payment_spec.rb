# frozen_string_literal: true

require 'model_helper'

RSpec.describe Payment, type: :model do
  it { is_expected.to have_timestamps }

  it { is_expected.to have_field(:amount).of_type(Float).with_default_value_of(0) }

  it { is_expected.to have_field(:fee).of_type(Float).with_default_value_of(0) }

  it { is_expected.to have_field(:refunded_amount).of_type(Float).with_default_value_of(0) }

  describe 'validations' do
    it { is_expected.to validate_inclusion_of(:gateway).to_allow(:stripe, :iugu, :transfeera, :woovi, nil) }
  end

  describe 'initial state' do
    it { is_expected.to be_pending }
  end

  describe '#has_final_step?' do
    subject { described_class.new aasm_state: state }

    context 'when captured' do
      let(:state) { 'captured' }

      it { is_expected.to have_final_step }
    end

    context 'when pending' do
      let(:state) { 'pending' }

      it { is_expected.not_to have_final_step }
    end

    context 'when accepted' do
      let(:state) { 'accepted' }

      it { is_expected.not_to have_final_step }
    end

    context 'when denied' do
      let(:state) { 'denied' }

      it { is_expected.to have_final_step }
    end

    context 'when refunded' do
      let(:state) { 'refunded' }

      it { is_expected.to have_final_step }
    end

    context 'when cancelled' do
      let(:state) { 'cancelled' }

      it { is_expected.to have_final_step }
    end

    context 'when :captured' do
      let(:state) { :captured }

      it { is_expected.to have_final_step }
    end

    context 'when :pending' do
      let(:state) { :pending }

      it { is_expected.not_to have_final_step }
    end

    context 'when :accepted' do
      let(:state) { :accepted }

      it { is_expected.not_to have_final_step }
    end

    context 'when :denied' do
      let(:state) { :denied }

      it { is_expected.to have_final_step }
    end

    context 'when :refunded' do
      let(:state) { :refunded }

      it { is_expected.to have_final_step }
    end

    context 'when :cancelled' do
      let(:state) { :cancelled }

      it { is_expected.to have_final_step }
    end
  end

  describe '#paid?' do
    subject { build :payment, aasm_state: state }

    context 'when state is pending' do
      let(:state) { 'pending' }

      it { is_expected.not_to be_paid }
    end

    context 'when state is created' do
      let(:state) { 'created' }

      it { is_expected.not_to be_paid }
    end

    context 'when state is refunded' do
      let(:state) { 'refunded' }

      it { is_expected.not_to be_paid }
    end

    context 'when state is cancelled' do
      let(:state) { 'cancelled' }

      it { is_expected.not_to be_paid }
    end

    context 'when state is denied' do
      let(:state) { 'denied' }

      it { is_expected.not_to be_paid }
    end

    context 'when state is failed' do
      let(:state) { 'failed' }

      it { is_expected.not_to be_paid }
    end

    context 'when state is accepted' do
      let(:state) { 'accepted' }

      it { is_expected.to be_paid }
    end

    context 'when state is captured' do
      let(:state) { 'captured' }

      it { is_expected.to be_paid }
    end
  end

  describe '#expired?' do
    subject { described_class.new bill_date: bill_date }

    context 'when there is bill_date' do
      context 'when date is valid' do
        let(:bill_date) { 1.day.from_now }

        it { is_expected.not_to be_expired }
      end

      it 'returns false when date is invalid' do
        payment = described_class.new bill_date: 1.day.ago
        expect(payment).to be_expired
      end

      it 'returns false when date is today' do
        payment = described_class.new bill_date: Time.zone.now
        expect(payment).not_to be_expired
      end
    end

    context 'when there is not bill_date' do
      let(:bill_date) { nil }

      it { is_expected.not_to be_expired }
    end
  end

  describe '#charge' do
    it 'sets state as created' do
      subject.charge
      is_expected.to be_created

      expect(subject.charged_at).not_to be_nil
    end
  end

  describe '#checkout' do
    subject { described_class.new aasm_state: :created }

    it 'sets state as accepted' do
      subject.checkout

      is_expected.to be_accepted

      expect(subject.accepted_at).not_to be_nil
    end
  end

  describe '#cancel' do
    context 'when accepted' do
      subject { described_class.new aasm_state: :accepted }

      it 'sets state as cancelled' do
        subject.cancel
        is_expected.to be_cancelled

        expect(subject.cancelled_at).not_to be_nil
      end
    end

    context 'when created' do
      subject { described_class.new aasm_state: :created }

      it 'sets state as cancelled' do
        subject.cancel
        is_expected.to be_cancelled

        expect(subject.cancelled_at).not_to be_nil
      end
    end

    context 'when pending' do
      it 'sets state as cancelled' do
        subject.cancel
        is_expected.to be_cancelled

        expect(subject.cancelled_at).not_to be_nil
      end
    end
  end

  describe '#capture' do
    context 'when created' do
      subject { described_class.new aasm_state: :created }

      it 'sets state as captured' do
        subject.capture
        is_expected.to be_captured

        expect(subject.captured_at).not_to be_nil
      end
    end

    context 'when accepted' do
      subject { described_class.new aasm_state: :accepted }

      it 'sets state as captured' do
        subject.capture
        is_expected.to be_captured

        expect(subject.captured_at).not_to be_nil
      end
    end
  end

  describe '#deny' do
    subject { described_class.new aasm_state: :created }

    it 'sets state as denied' do
      subject.deny
      is_expected.to be_denied

      expect(subject.denied_at).not_to be_nil
    end

    context 'when accepted' do
      subject { described_class.new aasm_state: :accepted }

      it 'sets state as denied' do
        subject.deny
        is_expected.to be_denied

        expect(subject.denied_at).not_to be_nil
      end
    end

    context 'when pending' do
      subject { described_class.new aasm_state: :pending }

      it 'sets state as denied' do
        subject.deny
        is_expected.to be_denied

        expect(subject.denied_at).not_to be_nil
      end
    end
  end

  describe '#refund' do
    subject { described_class.new aasm_state: :captured }

    it 'sets state as refunded' do
      subject.refund
      is_expected.to be_refunded

      expect(subject.refunded_at).not_to be_nil
    end

    context 'when accepted' do
      subject { described_class.new aasm_state: :accepted }

      it 'sets state as refunded' do
        subject.refund
        is_expected.to be_refunded

        expect(subject.refunded_at).not_to be_nil
      end
    end
  end
end
