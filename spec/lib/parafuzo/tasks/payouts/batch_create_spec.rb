# frozen_string_literal: true

require 'task_helper'

RSpec.describe 'payouts:batch:create', type: :task do
  subject(:task) { Rake::Task['payouts:batch:create'] }

  before do
    allow(Parafuzo::Core::Queue).to receive(:publish)
    allow(Parafuzo::Core.logger).to receive(:info)
  end

  context 'without taskers to put in batch' do
    before { task.execute }

    it { expect(Parafuzo::Core::Queue).not_to have_received(:publish) }
  end

  context 'with taskers to put in batch' do
    let(:tasker) { create :tasker, banks: [build(:bank, :with_checked_account)] }
    let!(:entry_one) { create :payment_entry, tasker:, gateway: :transfeera, payout_state: 'processing', value: 100 }
    let!(:entry_two) { create :payment_entry, tasker:, gateway: :transfeera, payout_state: 'processing', value: 101 }
    let!(:entry_three) { create :payment_entry, tasker:, gateway: :transfeera, payout_state: 'processing', value: 102 }

    before { task.execute }

    it {
      expect(Parafuzo::Core::Queue).to have_received(:publish)
        .with('payments-do-pay', { transfers: [hash_including(entry_id: entry_one.id.to_s),
                                               hash_including(entry_id: entry_two.id.to_s),
                                               hash_including(entry_id: entry_three.id.to_s)] })
    }
  end
end
