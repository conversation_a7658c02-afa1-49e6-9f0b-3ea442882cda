# frozen_string_literal: true

require 'functions_helper'

require_relative '../../../lib/parafuzo/services/payout/batch_name_updater'
require_relative '../../../lib/parafuzo/services/payout/payment_status_updater'

RSpec.describe 'poseidon-payout', type: :functions do
  let(:event) do
    make_cloud_event payload,
                     source: '//pubsub.googleapis.com/projects/sample-project/topics/gcf-test',
                     type: 'google.cloud.pubsub.topic.v1.messagePublished'
  end
  let(:payload) do
    {
      '@type' => 'type.googleapis.com/google.pubsub.v1.PubsubMessage',
      'message' => { 'data' => Base64.encode64(data.to_json) }
    }
  end

  describe 'batch_name_updater' do
    let(:data) { { 'entry_ids' => [BSON::ObjectId.new.to_s, BSON::ObjectId.new.to_s], 'batch_name' => 'BATCH_NAME' } }

    it 'invokes service with data' do
      load_temporary 'app/functions/payout/app.rb' do
        allow(Services::Payout::BatchNameUpdater).to receive(:process).and_return(true)

        call_event 'poseidon-payout-batch_name_updater', event

        expect(Services::Payout::BatchNameUpdater).to have_received(:process).with(data)
      end
    end
  end

  describe 'payment_status_updater' do
    let(:data) { { 'entry_id' => BSON::ObjectId.new.to_s, 'status' => 'success' } }

    it 'invokes service with data' do
      load_temporary 'app/functions/payout/app.rb' do
        allow(Services::Payout::PaymentStatusUpdater).to receive(:process).and_return(true)

        call_event 'poseidon-payout-payment_status_updater', event

        expect(Services::Payout::PaymentStatusUpdater).to have_received(:process).with(data)
      end
    end
  end
end
