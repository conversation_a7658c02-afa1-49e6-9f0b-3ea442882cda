# frozen_string_literal: true

FactoryBot.define do
  factory :bank do
    agency { '0202' }
    account { '3711-4' }
    type { :savings }
    code { '237' }
    main { true }

    trait :w_x_digit do
      code { '001' }
      agency { '1458-3' }
      account { '110232-X' }
    end

    factory :bank_w_x_digit do
      w_x_digit
    end

    trait :with_checked_account do
      check { :success }
    end

    trait :with_errors do
      check { :fail }
      check_errors do
        '["BE01 - CPF/CNPJ do usuário recebedor não ' \
          'é consistente com o titular da conta transacional especificada"]'
      end
    end
  end
end
