GIT
  remote: https://github.com/parafuzo/resque-cleaner.git
  revision: 6b592742a11556a64e0aed5e466826f643b2fa1f
  specs:
    resque-cleaner (0.4.1)
      resque

PATH
  remote: app/functions/background_check
  specs:
    background_check (0.1.0)
      aasm (~> 5.5.0)
      bcrypt (~> 3.1.19)
      cpf_cnpj (~> 0.5.0)
      devise (~> 4.9.4)
      enumerate_it (~> 3.2)
      functions_framework (~> 1.6.0)
      geocoder (~> 1.8)
      mongo (~> 2.19.1)
      mongoid (~> 7.5.4)
      mongoid_paranoia (~> 0.6.0)
      mongoid_search (~> 0.4.0)
      redis (~> 4.8.1)
      redis-namespace (~> 1.11.0)
      resque (~> 2.6.0)
      resque-retry (~> 1.8.1)
      resque_solo (~> 0.5.0)

PATH
  remote: app/functions/home_maintenance
  specs:
    home_maintenance (0.1.0)
      aasm (~> 5.5.0)
      bcrypt (~> 3.1.19)
      cpf_cnpj (~> 0.5.0)
      devise (~> 4.9.4)
      enumerate_it (~> 3.2)
      functions_framework (~> 1.6.0)
      geocoder (~> 1.8)
      mongo (~> 2.19.1)
      mongoid (~> 7.5.4)
      mongoid_paranoia (~> 0.6.0)
      mongoid_search (~> 0.4.0)
      redis (~> 4.8.1)
      redis-namespace (~> 1.11.0)
      resque (~> 2.6.0)
      resque-retry (~> 1.8.1)
      resque_solo (~> 0.5.0)

PATH
  remote: app/functions/job_changes
  specs:
    job_changes (0.1.0)
      aasm (~> 5.5.0)
      bcrypt (~> 3.1.19)
      cpf_cnpj (~> 0.5.0)
      devise (~> 4.9.4)
      enumerate_it (~> 3.2)
      functions_framework (~> 1.6.0)
      geocoder (~> 1.8)
      mongo (~> 2.19.1)
      mongoid (~> 7.5.4)
      mongoid_paranoia (~> 0.6.0)
      mongoid_search (~> 0.4.0)
      redis (~> 4.8.1)
      redis-namespace (~> 1.11.0)
      resque (~> 2.6.0)
      resque-retry (~> 1.8.1)
      resque_solo (~> 0.5.0)

PATH
  remote: app/functions/mei_check
  specs:
    mei_check (0.1.0)
      aasm (~> 5.5.0)
      bcrypt (~> 3.1.19)
      cpf_cnpj (~> 0.5.0)
      devise (~> 4.9.4)
      enumerate_it (~> 3.2)
      functions_framework (~> 1.6.0)
      geocoder (~> 1.8)
      mongo (~> 2.19.1)
      mongoid (~> 7.5.4)
      mongoid_paranoia (~> 0.6.0)
      mongoid_search (~> 0.4.0)
      redis (~> 4.8.1)
      redis-namespace (~> 1.11.0)
      resque (~> 2.6.0)
      resque-retry (~> 1.8.1)
      resque_solo (~> 0.5.0)

PATH
  remote: app/functions/payout
  specs:
    payout (0.1.0)
      aasm (~> 5.5.0)
      bcrypt (~> 3.1.19)
      cpf_cnpj (~> 0.5.0)
      devise (~> 4.9.4)
      enumerate_it (~> 3.2)
      functions_framework (~> 1.6.0)
      geocoder (~> 1.8)
      mongo (~> 2.19.1)
      mongoid (~> 7.5.4)
      mongoid_paranoia (~> 0.6.0)
      mongoid_search (~> 0.4.0)
      redis (~> 4.8.1)
      redis-namespace (~> 1.11.0)
      resque (~> 2.6.0)
      resque-retry (~> 1.8.1)
      resque_solo (~> 0.5.0)

PATH
  remote: app/functions/pix_cashin
  specs:
    pix_cashin (0.1.0)
      aasm (~> 5.5.0)
      bcrypt (~> 3.1.19)
      functions_framework (~> 1.6.0)
      mongo (~> 2.19.1)
      mongoid (~> 7.5.4)
      mongoid_paranoia (~> 0.6.0)
      mongoid_search (~> 0.4.0)
      redis (~> 4.8.1)
      redis-namespace (~> 1.11.0)
      resque (~> 2.6.0)
      resque-retry (~> 1.8.1)
      resque_solo (~> 0.5.0)

PATH
  remote: app/functions/processed_feedback
  specs:
    processed_feedback (0.1.0)
      aasm (~> 5.5.0)
      bcrypt (~> 3.1.19)
      cpf_cnpj (~> 0.5.0)
      devise (~> 4.9.4)
      enumerate_it (~> 3.2)
      functions_framework (~> 1.6.0)
      geocoder (~> 1.8)
      google-apis-pubsub_v1 (~> 0.53.0)
      mongo (~> 2.19.1)
      mongoid (~> 7.5.4)
      mongoid_paranoia (~> 0.6.0)
      mongoid_search (~> 0.4.0)
      redis (~> 4.8.1)
      redis-namespace (~> 1.11.0)
      resque (~> 2.6.0)
      resque-retry (~> 1.8.1)
      resque_solo (~> 0.5.0)

GEM
  remote: https://rubygems.org/
  specs:
    aasm (5.5.0)
      concurrent-ruby (~> 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activemodel-serializers-xml (1.0.2)
      activemodel (> 5.x)
      activesupport (> 5.x)
      builder (~> 3.1)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ast (2.4.3)
    aws-eventstream (1.3.0)
    aws-partitions (1.1003.0)
    aws-sdk-core (3.212.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.95.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.170.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.10.1)
      aws-eventstream (~> 1, >= 1.0.2)
    babel-source (5.8.35)
    babel-transpiler (0.7.0)
      babel-source (>= 4.0, < 6)
      execjs (~> 2.0)
    base64 (0.2.0)
    bcrypt (3.1.19)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.1.8)
    binding_of_caller (1.0.1)
      debug_inspector (>= 1.2.0)
    bootsnap (1.18.3)
      msgpack (~> 1.2)
    bson (4.15.0)
    bugsnag (6.27.0)
      concurrent-ruby (~> 1.0)
    builder (3.3.0)
    bullet (8.0.5)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    business_time (0.13.0)
      activesupport (>= 3.2.0)
      tzinfo
    byebug (11.1.3)
    cancancan (3.5.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    cloud_events (0.7.1)
    coderay (1.1.3)
    coffee-rails (5.0.0)
      coffee-script (>= 2.2.0)
      railties (>= 5.2.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.3.5)
    cpf_cnpj (0.5.0)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    css_parser (1.14.0)
      addressable
    csv (3.3.0)
    database_cleaner-core (2.0.1)
    database_cleaner-mongoid (2.0.1)
      database_cleaner-core (~> 2.0.0)
      mongoid
    database_cleaner-redis (2.0.0)
      database_cleaner-core (~> 2.0.0)
      redis
    date (3.4.1)
    debug_inspector (1.2.0)
    declarative (0.0.20)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.5.1)
    docile (1.4.1)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    draper (4.0.2)
      actionpack (>= 5.0)
      activemodel (>= 5.0)
      activemodel-serializers-xml (>= 1.0)
      activesupport (>= 5.0)
      request_store (>= 1.0)
      ruby2_keywords
    enumerate_it (3.2.4)
      activesupport (>= *******)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    execjs (2.10.0)
    factory_bot (6.5.0)
      activesupport (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.10.0)
      faraday-net_http (>= 2.0, < 3.2)
      logger
    faraday-net_http (3.1.0)
      net-http
    faraday-retry (2.2.1)
      faraday (~> 2.0)
    fast-stemmer (1.0.2)
    ffi (1.17.0)
    fiber-storage (1.0.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    functions_framework (1.6.0)
      cloud_events (>= 0.7.0, < 2.a)
      puma (>= 4.3.0, < 7.a)
      rack (>= 2.1, < 4.a)
    gapic-common (0.21.1)
      faraday (>= 1.9, < 3.a)
      faraday-retry (>= 1.0, < 3.a)
      google-protobuf (~> 3.18)
      googleapis-common-protos (>= 1.4.0, < 2.a)
      googleapis-common-protos-types (>= 1.11.0, < 2.a)
      googleauth (~> 1.9)
      grpc (~> 1.59)
    geocoder (1.8.2)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-apis-core (0.15.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.1, < 3.a)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
      rexml
    google-apis-pubsub_v1 (0.53.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-cloud-core (1.7.0)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (2.1.1)
      faraday (>= 1.0, < 3.a)
    google-cloud-errors (1.4.0)
    google-cloud-logging (2.4.0)
      concurrent-ruby (~> 1.1)
      google-cloud-core (~> 1.5)
      google-cloud-logging-v2 (~> 0.0)
      stackdriver-core (~> 1.3)
    google-cloud-logging-v2 (0.13.0)
      gapic-common (>= 0.21.1, < 2.a)
      google-cloud-errors (~> 1.0)
    google-protobuf (3.25.5)
    googleapis-common-protos (1.5.0)
      google-protobuf (~> 3.18)
      googleapis-common-protos-types (~> 1.7)
      grpc (~> 1.41)
    googleapis-common-protos-types (1.14.0)
      google-protobuf (~> 3.18)
    googleauth (1.11.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    graphql (2.4.13)
      base64
      fiber-storage
      logger
    grpc (1.63.0)
      google-protobuf (~> 3.25)
      googleapis-common-protos-types (~> 1.0)
    hashdiff (1.1.2)
    holidays (8.8.0)
    htmlentities (4.3.4)
    http-cookie (1.0.4)
      domain_name (~> 0.5)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.11.3)
    jwt (2.9.3)
      base64
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    kaminari-mongoid (1.0.2)
      kaminari-core (~> 1.0)
      mongoid
    language_server-protocol (3.17.0.4)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.6.6)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    method_source (1.1.0)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2022.0105)
    mini_magick (5.1.0)
    mini_mime (1.1.5)
    mini_portile2 (2.8.8)
    minitest (5.25.5)
    mongo (2.19.3)
      bson (>= 4.14.1, < 5.0.0)
    mongoid (7.5.4)
      activemodel (>= 5.1, < 7.1, != 7.0.0)
      mongo (>= 2.10.5, < 3.0.0)
      ruby2_keywords (~> 0.0.5)
    mongoid-compatibility (1.0.0)
      activesupport
      mongoid (>= 2.0)
    mongoid-rspec (4.2.0)
      mongoid (>= 3.0, < 10.0)
      mongoid-compatibility (>= 0.5.1)
    mongoid_paranoia (0.6.0)
      mongoid (>= 7.3)
    mongoid_search (0.4.0)
      fast-stemmer (~> 1.0.0)
      mongoid (>= 5.0.0)
    mono_logger (1.1.2)
    msgpack (1.7.2)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    mustermann (3.0.0)
      ruby2_keywords (~> 0.0.1)
    net-http (0.4.1)
      uri
    net-imap (0.5.7)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    netrc (0.11.0)
    newrelic_rpm (9.16.1)
    nfe-io (0.3.2)
      rest-client (~> 2.0.2)
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    observer (0.1.2)
    orm_adapter (0.5.0)
    os (1.1.4)
    parallel (1.27.0)
    parser (*******)
      ast (~> 2.4.1)
      racc
    patron (0.13.3)
    premailer (1.19.0)
      addressable
      css_parser (>= 1.12.0)
      htmlentities (>= 4.0.0)
    premailer-rails (1.12.0)
      actionmailer (>= 3)
      net-smtp
      premailer (~> 1.7, >= 1.7.9)
    prism (1.4.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.10.1)
      byebug (~> 11.0)
      pry (>= 0.13, < 0.15)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.1)
    puma (6.4.3)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.17)
    rack-protection (3.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-test (2.2.0)
      rack (>= 1.3)
    rack-timeout (0.7.0)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.2.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    redis (4.8.1)
    redis-namespace (1.11.0)
      redis (>= 4)
    regexp_parser (2.10.0)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.5.1)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    resque (2.6.0)
      mono_logger (~> 1.0)
      multi_json (~> 1.0)
      redis-namespace (~> 1.6)
      sinatra (>= 0.9.2)
    resque-retry (1.8.1)
      resque (>= 1.25, < 3.0)
      resque-scheduler (>= 4.0, < 6.0)
    resque-scheduler (4.7.0)
      mono_logger (~> 1.0)
      redis (>= 3.3)
      resque (>= 1.27)
      rufus-scheduler (~> 3.2, != 3.3)
    resque_mailer (2.4.3)
      actionmailer (>= 3.0)
      activesupport (>= 3.0)
    resque_solo (0.5.0)
      resque (>= 1.25.0)
    rest-client (2.0.2)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    retriable (3.1.2)
    rexml (3.3.9)
    rollout (2.6.1)
      observer
      redis (>= 4.0, < 6)
    rouge (4.1.2)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.2)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-html-matchers (0.10.0)
      nokogiri (~> 1)
      rspec (>= 3.0.0.a)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.1.0)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.2)
    rubocop (1.75.5)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-capybara (2.21.0)
      rubocop (~> 1.41)
    rubocop-factory_bot (2.26.1)
      rubocop (~> 1.61)
    rubocop-graphql (1.5.4)
      rubocop (>= 1.50, < 2)
    rubocop-performance (1.23.0)
      rubocop (>= 1.48.1, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails (2.27.0)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.52.0, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rake (0.6.0)
      rubocop (~> 1.0)
    rubocop-rspec (3.2.0)
      rubocop (~> 1.61)
    rubocop-rspec_rails (2.30.0)
      rubocop (~> 1.61)
      rubocop-rspec (~> 3, >= 3.0.1)
    ruby-progressbar (1.13.0)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    rufus-scheduler (3.8.2)
      fugit (~> 1.1, >= 1.1.6)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    selenium-webdriver (4.27.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simple_form (5.3.1)
      actionpack (>= 5.2)
      activemodel (>= 5.2)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    sinatra (3.1.0)
      mustermann (~> 3.0)
      rack (~> 2.2, >= 2.2.4)
      rack-protection (= 3.1.0)
      tilt (~> 2.0)
    sinatra-contrib (3.1.0)
      multi_json
      mustermann (~> 3.0)
      rack-protection (= 3.1.0)
      sinatra (= 3.1.0)
      tilt (~> 2.0)
    sinatra-cross_origin (0.4.0)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    stackdriver-core (1.6.0)
      google-cloud-core (~> 1.2)
    stimulus-rails (1.2.2)
      railties (>= 6.0.0)
    stringio (3.1.7)
    stripe (12.6.0)
    thor (1.3.2)
    tilt (2.3.0)
    timecop (0.9.10)
    timeout (0.4.3)
    trailblazer-option (0.1.2)
    turbo-rails (2.0.12)
      actionpack (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    uglifier (4.2.1)
      execjs (>= 0.3.0, < 3)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uniform_notifier (1.16.0)
    uri (1.0.3)
    vcr (6.3.1)
      base64
    warden (1.2.9)
      rack (>= 2.0.9)
    webmock (3.24.0)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket (1.2.11)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    yard (0.9.37)
    zeitwerk (2.7.1)

PLATFORMS
  ruby

DEPENDENCIES
  aasm (~> 5.5.0)
  aws-sdk-s3 (~> 1.170)
  babel-transpiler (~> 0.7.0)
  background_check!
  bcrypt (~> 3.1.19)
  better_errors (~> 2.10)
  binding_of_caller (~> 1.0)
  bootsnap (~> 1.18.3)
  bugsnag (~> 6.27)
  bullet (~> 8.0)
  business_time (~> 0.13.0)
  cancancan (~> 3.5.0)
  capybara (~> 3.40)
  coffee-rails (~> 5.0.0)
  cpf_cnpj (~> 0.5.0)
  database_cleaner-mongoid (~> 2.0)
  database_cleaner-redis (~> 2.0)
  devise (~> 4.9.4)
  draper (~> 4.0.2)
  enumerate_it (~> 3.2)
  factory_bot (~> 6.5)
  faker (~> 3.5.1)
  geocoder (~> 1.8)
  google-apis-pubsub_v1 (~> 0.53.0)
  google-cloud-logging (~> 2.4.0)
  graphql (~> 2.4.13)
  holidays (~> 8.8)
  home_maintenance!
  httparty (~> 0.22.0)
  importmap-rails (~> 2.1.0)
  jbuilder (~> 2.13)
  job_changes!
  jquery-rails (~> 4.6.0)
  jwt (~> 2.9)
  kaminari-actionview (~> 1.2.2)
  kaminari-core (~> 1.2.2)
  kaminari-mongoid (~> 1.0.1)
  listen (~> 3.9.0)
  mei_check!
  mini_magick (~> 5.1.0)
  mongo (~> 2.19.1)
  mongoid (~> 7.5.4)
  mongoid-rspec (~> 4.2)
  mongoid_paranoia (~> 0.6.0)
  mongoid_search (~> 0.4.0)
  newrelic_rpm (~> 9.16.1)
  nfe-io (~> 0.3.2)
  nokogiri (~> 1.18)
  patron (~> 0.13.1)
  payout!
  pix_cashin!
  premailer-rails (~> 1.12.0)
  processed_feedback!
  pry (~> 0.14)
  pry-byebug (~> 3.10.1)
  psych (~> 5.2.6)
  puma (~> 6.4.3)
  rack-test (~> 2.2)
  rack-timeout (~> 0.7.0)
  rails (~> *******)
  rails-controller-testing (~> 1.0.5)
  rake (~> 13.2.1)
  redis (~> 4.8.1)
  redis-namespace (~> 1.11.0)
  resque (~> 2.6.0)
  resque-cleaner!
  resque-retry (~> 1.8.1)
  resque-scheduler (~> 4.7.0)
  resque_mailer (~> 2.4.3)
  resque_solo (~> 0.5.0)
  rollout (~> 2.6.1)
  rspec (~> 3.13)
  rspec-html-matchers (~> 0.10)
  rspec-rails (~> 7.1)
  rubocop (~> 1.75)
  rubocop-capybara (~> 2.0)
  rubocop-factory_bot (~> 2.0)
  rubocop-graphql (~> 1.5)
  rubocop-performance (~> 1.23)
  rubocop-rails (~> 2.27)
  rubocop-rake (~> 0.6)
  rubocop-rspec (~> 3.2)
  rubocop-rspec_rails (~> 2.0)
  sass-rails (~> 6.0.0)
  selenium-webdriver (~> 4.27.0)
  simple_form (~> 5.3.1)
  simplecov (~> 0.22)
  simplecov_json_formatter (~> 0.1.4)
  sinatra (~> 3.1.0)
  sinatra-contrib (~> 3.1.0)
  sinatra-cross_origin (~> 0.4.0)
  sprockets (~> 4.2)
  stimulus-rails (~> 1.2.1)
  stripe (~> 12.6.0)
  timecop (~> 0.9)
  turbo-rails (~> 2.0.12)
  uglifier (>= 1.3.0)
  vcr (~> 6.3)
  warden (~> 1.2.9)
  webmock (~> 3.24.0)
  yard (~> 0.9)

RUBY VERSION
   ruby 3.3.7p123

BUNDLED WITH
   2.6.5
