# frozen_string_literal: true

RACK_ENV = ENV.fetch('RACK_ENV', 'development') unless Object.const_defined? :RACK_ENV

require File.expand_path('config/boot', __dir__)

require 'resque/tasks'
require 'resque/scheduler/tasks'
require 'mongoid/tasks/database'

load 'mongoid/tasks/database.rake'
load 'lib/parafuzo/tasks/payouts/batch_create.rake'
load 'lib/parafuzo/tasks/payouts/weekly_pay.rake'

namespace :db do
  namespace :mongoid do
    task :environment do
      mongoid_file = File.expand_path('config/mongoid.yml', __dir__)
      mongoid_conf = YAML.safe_load(ERB.new(File.read(mongoid_file)).result, aliases: true)[APP_ENV]
      puts "MONGOID: Conf for env #{APP_ENV}", mongoid_conf
      Mongoid.load_configuration(mongoid_conf)
    end

    desc 'Build search index'
    task search_index: :environment do
      Mongoid::Search.classes.each do |klass|
        Mongoid::Search::Log.silent = ENV.fetch('SILENT', nil)
        Mongoid::Search::Log.log "\nIndexing documents for #{klass.name}:\n"
        klass.index_keywords!
      end
      Mongoid::Search::Log.log "\n\nDone.\n"
    end
  end

  desc 'Populate the database'
  task :seed do
    Dir.glob('db/configs/*.rb').each do |file|
      require File.expand_path(file)
    end

    Rake::Task['db:update_services'].invoke

    Dir.glob('db/seeds/*.rb').each do |file|
      require File.expand_path(file)
    end

    AdminUser.create!(email: '<EMAIL>', password: '12345678', name: 'Dev', role: 'admin')
  end

  desc 'Update services.'
  task :update_services do
    Dir.glob('db/services/*.rb').each do |file|
      require File.expand_path(file)
    end
  end

  desc 'Update services.'
  task :update_service, [:service] do |task, args|
    require File.expand_path("db/services/#{args[:service]}.rb")
  end

  desc 'Run specific migration.'
  task :migrate, [:file] do |task, args|
    require File.expand_path("db/migrations/#{args[:file]}.rb")
  end
end

namespace :resque do
  task :setup do
    require 'resque'
    require 'resque-scheduler'

    ENV['QUEUE'] ||= '*'

    # If you want to be able to dynamically change the schedule,
    # uncomment this line.  A dynamic schedule can be updated via the
    # Resque::Scheduler.set_schedule (and remove_schedule) methods.
    # When dynamic is set to true, the scheduler process looks for
    # schedule changes and applies them on the fly.
    # Note: This feature is only available in >=2.0.0.
    # Resque::Scheduler.dynamic = true

    # The schedule doesn't need to be stored in a YAML, it just needs to
    # be a hash.  YAML is usually the easiest.
    # Resque.schedule = YAML.load_file('your_resque_schedule.yml')
  end
end

namespace :coverage do
  desc 'Collates all result sets generated by the different test runners'
  task :report do # rubocop:disable Rails/RakeEnvironment
    require 'simplecov'
    require 'simplecov_json_formatter'

    SimpleCov.collate Dir['coverage/.resultset-*.json'], 'rails' do
      formatter SimpleCov::Formatter::JSONFormatter
    end
  end
end
