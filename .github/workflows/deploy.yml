name: deploy

on:
  workflow_dispatch:
  release:
    types: [published]
  push:
    branches:
      - qa

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  PROJECT_ID: ${{ github.event_name == 'release' && github.event.action == 'published' && 'parafuzo-infra' || 'parafuzo-qa-infra' }}

jobs:
  build:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - name: setup buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: gcr.io/${{ env.PROJECT_ID }}/poseidon
          tags: |
            type=semver,pattern={{version}},prefix=v
            type=raw,value=${{ github.sha }}

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - run: |
          gcloud auth configure-docker -q

      - name: build
        id: docker_build
        uses: docker/build-push-action@v6
        with:
          push: true
          build-args: RELEASE_VERSION=production
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=registry,ref=gcr.io/${{ env.PROJECT_ID }}/poseidon:latest
          cache-to: type=inline

  deploy-on-gke:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    needs: [build]

    strategy:
      matrix:
        deployment:
          - name: poseidon-scheduler
          - name: poseidon-worker
          - name: poseidon-job-worker
            only_for: production
          - name: poseidon-notify-worker
            only_for: production
          - name: poseidon-payment-worker
            only_for: production
          - name: poseidon-webhook-worker
            only_for: production
          - name: poseidon-urgent-worker
            only_for: production
          - name: poseidon-high-worker
            only_for: production
          - name: poseidon-normal-worker
            only_for: production
          - name: poseidon-low-worker
            only_for: production

    steps:
      - name: config
        id: config
        run: |
          echo "CLUSTER_ENVIRONMENT=${{ github.event_name == 'release' && github.event.action == 'published' && 'production' || 'qa' }}" >> $GITHUB_ENV
          echo ::set-output name=tag::$(echo "gcr.io/${{ env.PROJECT_ID }}/poseidon:${{ github.event_name == 'release' && github.event.action == 'published' && github.event.release.tag_name || github.sha }}")

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - id: "get-credentials"
        uses: "google-github-actions/get-gke-credentials@v2"
        with:
          project_id: ${{ env.PROJECT_ID }}
          cluster_name: ${{ env.CLUSTER_ENVIRONMENT == 'qa' && 'pfz-qa-auto-cluster' || 'pfz-prod-auto-cluster' }}
          location: "us-east1"

      - name: ${{ matrix['deployment'].name }}
        if: ${{ !matrix['deployment'].only_for || matrix['deployment'].only_for == env.CLUSTER_ENVIRONMENT }}
        run: |
          kubectl set image deployment/${{ matrix['deployment'].name }} poseidon="${{ steps.config.outputs.tag }}"

  deploy-on-run:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    needs: [build]

    strategy:
      matrix:
        deployment:
          - name: poseidon-admin
          - name: poseidon-api

    steps:
      - name: config
        id: config
        run: |
          echo ::set-output name=tag::$(echo "gcr.io/${{ env.PROJECT_ID }}/poseidon:${{ github.event_name == 'release' && github.event.action == 'published' && github.event.release.tag_name || github.sha }}")

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: ${{ matrix['deployment'].name }}
        run: |
          gcloud run deploy ${{ matrix['deployment'].name }} --region=us-east1 --image="${{ steps.config.outputs.tag }}" --project="${{ env.PROJECT_ID }}"

  deploy-on-run-jobs:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    needs: [build]

    strategy:
      matrix:
        deployment:
          - name: poseidon-payouts-weekly-pay

    steps:
      - name: config
        id: config
        run: |
          echo ::set-output name=tag::$(echo "gcr.io/${{ env.PROJECT_ID }}/poseidon:${{ github.event_name == 'release' && github.event.action == 'published' && github.event.release.tag_name || github.sha }}")

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: ${{ matrix['deployment'].name }}
        run: |
          gcloud run jobs deploy ${{ matrix['deployment'].name }} --region=us-east1 --image="${{ steps.config.outputs.tag }}" --project="${{ env.PROJECT_ID }}"

  deploy-on-functions:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    needs: [build]

    strategy:
      matrix:
        deployment:
          - name: poseidon-home_maintenance-user_updater
            source_dir: app/functions/home_maintenance
            trigger_topic: home-maintenance-on-customers-sent
            max_instances: 5
          - name: poseidon-pix_cashin-confirm
            source_dir: app/functions/pix_cashin
            trigger_topic: payments-on-pix-cashin
            max_instances: 10
          - name: poseidon-payout-batch_name_updater
            source_dir: app/functions/payout
            trigger_topic: payments-on-batch-create
            max_instances: 1
          - name: poseidon-payout-payment_status_updater
            source_dir: app/functions/payout
            trigger_topic: payments-on-status-update
            max_instances: 5
          - name: poseidon-background_check-tasker_updater
            source_dir: app/functions/background_check
            trigger_topic: background-check-on-entities-sent
            max_instances: 10
          - name: poseidon-gemini-save_processed_feedback
            source_dir: app/functions/processed_feedback
            trigger_topic: gemini-on-feedback-processed
            max_instances: 10
          - name: poseidon-mei_check-tasker_company_updater
            source_dir: app/functions/mei_check
            trigger_topic: bigdatacorp-on-mei-check-sent
            max_instances: 10
          - name: poseidon-job_changes-tasker_updater
            source_dir: app/functions/job_changes
            trigger_topic: mongodb-on-changed
            max_instances: 10
    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: ${{ matrix['deployment'].name }}
        run: |
          bin/functions-remote-deploy ${{ matrix['deployment'].name }} \
                                      --project ${{ env.PROJECT_ID }} \
                                      --source-dir ${{ matrix['deployment'].source_dir }} \
                                      --trigger-topic ${{ matrix['deployment'].trigger_topic }} \
                                      --max-instances ${{ matrix['deployment'].max_instances }}

  newrelic:
    runs-on: ubuntu-latest
    needs: [deploy-on-gke, deploy-on-run, deploy-on-functions]
    name: New Relic
    if: ${{ github.event_name == 'release' && github.event.action == 'published' }}
    steps:
      - name: Set Release Version from Tag
        run: echo "RELEASE_VERSION=${{ github.ref_name }}" >> $GITHUB_ENV
      - name: New Relic Application Deployment Marker
        uses: newrelic/deployment-marker-action@v2.5.1
        with:
          apiKey: ${{ secrets.NEW_RELIC_API_KEY }}
          guid: ${{ secrets.NEW_RELIC_DEPLOYMENT_ENTITY_GUID }}
          version: "${{ env.RELEASE_VERSION }}"
          user: "${{ github.actor }}"
